<%- include('layout', {
    path: '/admin/stats',
    success: typeof success !== 'undefined' ? success : '',
    error: typeof error !== 'undefined' ? error : '',
    info: typeof info !== 'undefined' ? info : '',
    body: `
<div class="admin-page-header">
    <h1>Statistiques</h1>
    <p>Vue d'ensemble des statistiques du site</p>
</div>

<div class="admin-stats-container">
    <div class="admin-stats-grid">
        ${stats.map(stat => `
            <div class="admin-stat-card">
                <div class="admin-stat-icon">
                    <i class="fas fa-${
                        stat.label === 'Utilisateurs' ? 'users' :
                        stat.label === 'Administrateurs' ? 'user-shield' :
                        stat.label === 'Livres' ? 'book' :
                        stat.label === 'Genres' ? 'tags' :
                        'chart-bar'
                    }"></i>
                </div>
                <div class="admin-stat-content">
                    <h3>${stat.label}</h3>
                    <p class="admin-stat-value">${stat.count}</p>
                </div>
            </div>
        `).join('')}
    </div>

    <div class="admin-chart-container">
        <h2>Répartition des utilisateurs</h2>
        <div class="admin-chart">
            <canvas id="userChart"></canvas>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Récupérer les données des statistiques
    const adminCount = ${stats.find(s => s.label === 'Administrateurs')?.count || 0};
    const userCount = ${stats.find(s => s.label === 'Utilisateurs')?.count || 0} - adminCount;

    // Créer le graphique
    const ctx = document.getElementById('userChart').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Lecteurs', 'Administrateurs'],
            datasets: [{
                data: [userCount, adminCount],
                backgroundColor: [
                    '#4a6fa5',
                    '#f39c12'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
</script>
` }) %>
