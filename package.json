{"name": "nookli-app", "version": "1.0.0", "description": "Nookli - <PERSON> Coin Lecture : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> : tous vos livres et histoires au même endroit.", "type": "module", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "create-admin": "node scripts/create-admin.js", "seed-books": "node scripts/seed-book-db.js", "optimize-db": "node scripts/optimize-database.js"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "connect-sqlite3": "^0.9.15", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-fileupload": "^1.5.1", "express-flash": "^0.0.2", "express-session": "^1.18.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.1.10", "sqlite3": "^5.1.7"}}