/**
 * Utilitaires pour transformer les callbacks SQLite en Promises
 * Facilite l'utilisation de async/await avec SQLite
 */

/**
 * Exécute une requête SQLite et retourne une Promise
 * @param {Object} db - Instance de la base de données SQLite
 * @param {string} query - Requête SQL à exécuter
 * @param {Array} params - Paramètres de la requête
 * @returns {Promise<Object>} - Résultat de la requête
 */
export const run = (db, query, params = []) => {
    return new Promise((resolve, reject) => {
        db.run(query, params, function(err) {
            if (err) return reject(err);
            resolve({ lastID: this.lastID, changes: this.changes });
        });
    });
};

/**
 * Exécute une requête SQLite et retourne une seule ligne
 * @param {Object} db - Instance de la base de données SQLite
 * @param {string} query - Requête SQL à exécuter
 * @param {Array} params - Paramètres de la requête
 * @returns {Promise<Object|null>} - Ligne résultante ou null
 */
export const get = (db, query, params = []) => {
    return new Promise((resolve, reject) => {
        db.get(query, params, (err, row) => {
            if (err) return reject(err);
            resolve(row || null);
        });
    });
};

/**
 * Exécute une requête SQLite et retourne toutes les lignes
 * @param {Object} db - Instance de la base de données SQLite
 * @param {string} query - Requête SQL à exécuter
 * @param {Array} params - Paramètres de la requête
 * @returns {Promise<Array>} - Lignes résultantes
 */
export const all = (db, query, params = []) => {
    return new Promise((resolve, reject) => {
        db.all(query, params, (err, rows) => {
            if (err) return reject(err);
            resolve(rows || []);
        });
    });
};

/**
 * Exécute plusieurs requêtes SQL en une seule transaction
 * @param {Object} db - Instance de la base de données SQLite
 * @param {string} query - Requêtes SQL à exécuter
 * @returns {Promise<void>} - Promise résolue lorsque la transaction est terminée
 */
export const exec = (db, query) => {
    return new Promise((resolve, reject) => {
        db.exec(query, (err) => {
            if (err) return reject(err);
            resolve();
        });
    });
};

export default {
    run,
    get,
    all,
    exec
};
