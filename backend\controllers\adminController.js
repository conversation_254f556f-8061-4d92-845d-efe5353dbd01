import { User, Book, Genre } from '../models/index.js';
import { Op } from 'sequelize';

/**
 * Affiche le tableau de bord administrateur
 */
export async function showDashboard(req, res) {
    try {
        // Récupérer les statistiques de base pour le dashboard
        const [userCount, bookCount, genreCount] = await Promise.all([
            // Nombre total d'utilisateurs
            User.count(),
            // Nombre total de livres
            Book.count(),
            // Nombre total de genres
            Genre.count()
        ]);

        res.render('admin/dashboard', {
            title: 'Administration - Nookli',
            currentUser: req.user,
            stats: {
                userCount,
                bookCount,
                genreCount
            }
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des statistiques:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement du tableau de bord.');
        res.render('admin/dashboard', {
            title: 'Administration - Nookli',
            currentUser: req.user,
            stats: {
                userCount: 0,
                bookCount: 0,
                genreCount: 0
            }
        });
    }
}

/**
 * Affiche la liste des livres pour l'administration
 */
export async function showBooks(req, res) {
    try {
        // Récupérer les paramètres de recherche et de filtre
        const search = req.query.search || '';
        const genreId = req.query.genre_id || '';
        const minRating = req.query.min_rating || '';
        const maxRating = req.query.max_rating || '';
        const year = req.query.year || '';
        const status = req.query.status || '';
        const sortBy = req.query.sortBy || 'title';
        const sortOrder = req.query.sortOrder || 'ASC';

        // Construire les filtres
        const filters = {};
        if (genreId) filters.genre_id = genreId;
        if (minRating) filters.min_rating = parseFloat(minRating);
        if (year) filters.publication_year = parseInt(year);
        if (status) filters.status = status;

        // Options de pagination et tri
        const options = {
            orderBy: sortBy,
            order: sortOrder,
            includeAllStatuses: true // L'admin voit tous les livres, y compris les brouillons
        };

        // Récupérer tous les genres pour les filtres
        const genres = await Genre.findAll();

        let books = [];

        // Si on a un terme de recherche, utiliser la méthode search
        if (search) {
            try {
                const result = await Book.search(search, filters, options);
                books = result.books || [];
            } catch (err) {
                console.error('Erreur lors de la recherche des livres:', err);
                req.flash('error', 'Une erreur est survenue lors de la recherche des livres.');
            }
        } else {
            // Sinon, utiliser la méthode findFiltered
            try {
                const result = await Book.findFiltered(filters, options);
                books = result.books || [];
            } catch (err) {
                console.error('Erreur lors de la récupération des livres:', err);
                req.flash('error', 'Une erreur est survenue lors du chargement des livres.');
            }
        }

        res.render('admin/books', {
            title: 'Gestion des livres - Administration - Nookli',
            currentUser: req.user,
            books,
            filters: { search, genreId, minRating, maxRating, year, status, sortBy, sortOrder },
            genres
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des genres:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des genres.');
        res.render('admin/books', {
            title: 'Gestion des livres - Administration - Nookli',
            currentUser: req.user,
            books: [],
            filters: {
                search: req.query.search || '',
                genreId: req.query.genre_id || '',
                minRating: req.query.min_rating || '',
                maxRating: req.query.max_rating || '',
                year: req.query.year || '',
                status: req.query.status || '',
                sortBy: req.query.sortBy || 'title',
                sortOrder: req.query.sortOrder || 'ASC'
            },
            genres: []
        });
    }
}

/**
 * Affiche le formulaire d'ajout de livre
 */
export async function showBookForm(req, res) {
    try {
        // Récupérer tous les genres pour le formulaire
        let genres = await Genre.findAll({
            order: [['name', 'ASC']]
        });

        res.render('admin/book-form-temp', {
            title: 'Ajouter un livre - Administration - Nookli',
            currentUser: req.user,
            book: {},
            genres: genres || [],
            formAction: '/admin/books/new',
            formTitle: 'Ajouter un nouveau livre'
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des genres:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des genres.');
        return res.redirect('/admin/books');
    }
}

/**
 * Crée un nouveau livre
 */
export async function createBook(req, res) {
    try {
        const {
            title,
            author,
            description,
            summary,
            genre_id,
            new_genre_name,
            cover_image_url,
            pdf_url,
            publication_year,
            isbn,
            initial_rating,
            language,
            status
        } = req.body;

        // Validation de base
        if (!title || !author) {
            req.flash('error', 'Le titre et l\'auteur sont obligatoires.');
            return res.redirect('/admin/books/new');
        }

        // Gérer la création d'un nouveau genre si nécessaire
        let finalGenreId = genre_id;

        if (genre_id === 'new' && new_genre_name) {
            try {
                // Vérifier si un genre avec ce nom existe déjà
                const existingGenre = await Genre.findOne({
                    where: {
                        name: {
                            [Op.like]: new_genre_name
                        }
                    }
                });

                if (existingGenre) {
                    // Utiliser le genre existant
                    finalGenreId = existingGenre.id;
                } else {
                    // Créer un nouveau genre
                    const newGenre = await Genre.create({
                        name: new_genre_name,
                        description: `Genre créé lors de l'ajout du livre "${title}"`
                    });

                    finalGenreId = newGenre.id;
                    req.flash('info', `Le genre "${new_genre_name}" a été créé avec succès.`);
                }
            } catch (error) {
                console.error('Erreur lors de la création du genre:', error);
                req.flash('error', 'Une erreur est survenue lors de la création du genre.');
                return res.redirect('/admin/books/new');
            }
        }

        // Gérer les fichiers uploadés
        // Ne pas utiliser l'URL de couverture si elle est vide ou invalide
        // Vérifier si l'URL est valide (commence par http:// ou https://) ou si c'est un chemin local valide
        let finalCoverImageUrl = null;
        if (cover_image_url && cover_image_url.trim() !== '') {
            if (cover_image_url.match(/^https?:\/\/.+/) || cover_image_url.startsWith('/')) {
                finalCoverImageUrl = cover_image_url;
            }
        }
        let finalPdfUrl = pdf_url || null;

        // Générer le slug manuellement
        const bookData = {
            title,
            author,
            // Si aucune langue n'est sélectionnée, on utilise une chaîne vide pour le slug
            // mais 'fr' sera utilisé comme valeur par défaut lors de la création du livre
            language: language || '',
            publication_year: publication_year || null
        };
        const slug = Book.generateSlug(bookData);

        // Traiter l'image de couverture
        if (req.files && req.files.cover_image_file) {
            try {
                const coverFile = req.files.cover_image_file;

                // Vérifier le type de fichier
                if (!coverFile.mimetype.startsWith('image/')) {
                    req.flash('error', 'Le fichier de couverture doit être une image.');
                    return res.redirect('/admin/books/new');
                }

                // Vérifier la taille du fichier (max 2MB)
                if (coverFile.size > 2 * 1024 * 1024) {
                    req.flash('error', 'L\'image de couverture ne doit pas dépasser 2MB.');
                    return res.redirect('/admin/books/new');
                }

                // Créer un nom de fichier sécurisé basé sur le slug
                const fileManager = await import('../utils/fileManager.js');
                const coverInfo = await fileManager.default.saveCoverImage(coverFile, slug);
                finalCoverImageUrl = coverInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du traitement de l\'image de couverture:', error);
                req.flash('error', 'Une erreur est survenue lors du traitement de l\'image de couverture.');
                return res.redirect('/admin/books/new');
            }
        } else if (cover_image_url) {
            // Si une URL est fournie, télécharger l'image
            try {
                const fileManager = await import('../utils/fileManager.js');
                const coverInfo = await fileManager.default.downloadCoverImage(cover_image_url, slug);
                finalCoverImageUrl = coverInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du téléchargement de l\'image de couverture:', error);
                // Continuer avec l'URL fournie si le téléchargement échoue
                finalCoverImageUrl = cover_image_url;
            }
        }

        // Traiter le fichier PDF
        if (req.files && req.files.pdf_file) {
            try {
                const pdfFile = req.files.pdf_file;

                // Vérifier le type de fichier
                if (pdfFile.mimetype !== 'application/pdf') {
                    req.flash('error', 'Le fichier doit être au format PDF.');
                    return res.redirect('/admin/books/new');
                }

                // Vérifier la taille du fichier (max 10MB)
                if (pdfFile.size > 10 * 1024 * 1024) {
                    req.flash('error', 'Le fichier PDF ne doit pas dépasser 10MB.');
                    return res.redirect('/admin/books/new');
                }

                // Créer un nom de fichier sécurisé basé sur le slug
                const fileManager = await import('../utils/fileManager.js');
                const pdfInfo = await fileManager.default.saveEbookFile(pdfFile, slug);
                finalPdfUrl = pdfInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du traitement du fichier PDF:', error);
                req.flash('error', 'Une erreur est survenue lors du traitement du fichier PDF.');
                return res.redirect('/admin/books/new');
            }
        } else if (pdf_url) {
            // Si une URL est fournie, télécharger le PDF
            try {
                const fileManager = await import('../utils/fileManager.js');
                const pdfInfo = await fileManager.default.downloadEbook(pdf_url, slug);
                finalPdfUrl = pdfInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du téléchargement du fichier PDF:', error);
                // Continuer avec l'URL fournie si le téléchargement échoue
                finalPdfUrl = pdf_url;
            }
        }

        // Supprimé car déplacé plus haut

        // Créer le livre avec Sequelize
        await Book.create({
            title,
            author,
            description: description || null,
            summary: summary || null,
            genre_id: finalGenreId || null,
            cover_image_url: finalCoverImageUrl,
            pdf_url: finalPdfUrl,
            publication_year: publication_year || null,
            isbn: isbn || null,
            initial_rating: initial_rating || 0,
            average_rating: initial_rating || 0, // Utiliser la note initiale comme note moyenne au départ
            language: language || 'fr', // Français par défaut si aucune langue n'est sélectionnée
            status: status || 'draft',
            created_by: req.user ? req.user.id : null,
            slug // Ajouter le slug généré
        });

        req.flash('success', `Le livre "${title}" a été ajouté avec succès.`);
        res.redirect('/admin/books');
    } catch (err) {
        console.error('Erreur lors de la création du livre:', err);
        req.flash('error', 'Une erreur est survenue lors de la création du livre.');
        return res.redirect('/admin/books/new');
    }
}

/**
 * Affiche le formulaire de modification d'un livre
 */
export async function showEditBookForm(req, res) {
    try {
        const bookId = req.params.id;

        // Récupérer le livre et tous les genres
        const [book, genres] = await Promise.all([
            // Récupérer le livre avec le genre et le créateur
            Book.findByPk(bookId, {
                include: [
                    { model: Genre, as: 'genre' },
                    { model: User, as: 'creator', attributes: ['id', 'username'] }
                ]
            }),
            // Récupérer tous les genres
            Genre.findAll({
                order: [['name', 'ASC']]
            })
        ]);

        if (!book) {
            req.flash('error', 'Le livre demandé n\'existe pas.');
            return res.redirect('/admin/books');
        }

        res.render('admin/book-form-temp', {
            title: `Modifier ${book.title} - Administration - Nookli`,
            currentUser: req.user,
            book,
            genres,
            formAction: `/admin/books/edit/${bookId}`,
            formTitle: 'Modifier le livre'
        });
    } catch (err) {
        console.error('Erreur lors de la récupération du livre:', err);
        req.flash('error', 'Le livre demandé n\'existe pas ou une erreur est survenue.');
        res.redirect('/admin/books');
    }
}

/**
 * Met à jour un livre existant
 */
export async function updateBook(req, res) {
    try {
        const bookId = req.params.id;
        const {
            title,
            author,
            description,
            summary,
            genre_id,
            new_genre_name,
            cover_image_url,
            pdf_url,
            publication_year,
            isbn,
            initial_rating,
            language,
            status
        } = req.body;

        // Validation de base
        if (!title || !author) {
            req.flash('error', 'Le titre et l\'auteur sont obligatoires.');
            return res.redirect(`/admin/books/edit/${bookId}`);
        }

        // Gérer la création d'un nouveau genre si nécessaire
        let finalGenreId = genre_id;

        if (genre_id === 'new' && new_genre_name) {
            try {
                // Vérifier si un genre avec ce nom existe déjà
                const existingGenre = await Genre.findOne({
                    where: {
                        name: {
                            [Op.like]: new_genre_name
                        }
                    }
                });

                if (existingGenre) {
                    // Utiliser le genre existant
                    finalGenreId = existingGenre.id;
                } else {
                    // Créer un nouveau genre
                    const newGenre = await Genre.create({
                        name: new_genre_name,
                        description: `Genre créé lors de la modification du livre "${title}"`
                    });

                    finalGenreId = newGenre.id;
                    req.flash('info', `Le genre "${new_genre_name}" a été créé avec succès.`);
                }
            } catch (error) {
                console.error('Erreur lors de la création du genre:', error);
                req.flash('error', 'Une erreur est survenue lors de la création du genre.');
                return res.redirect(`/admin/books/edit/${bookId}`);
            }
        }

        // Récupérer le livre existant
        const book = await Book.findByPk(bookId);

        if (!book) {
            req.flash('error', 'Le livre demandé n\'existe pas.');
            return res.redirect('/admin/books');
        }

        // Gérer les fichiers uploadés
        // Ne pas utiliser l'URL de couverture si elle est vide ou invalide
        // Vérifier si l'URL est valide (commence par http:// ou https://) ou si c'est un chemin local valide
        let finalCoverImageUrl = book.cover_image_url;
        if (cover_image_url && cover_image_url.trim() !== '') {
            if (cover_image_url.match(/^https?:\/\/.+/) || cover_image_url.startsWith('/')) {
                finalCoverImageUrl = cover_image_url;
            }
        }
        let finalPdfUrl = pdf_url || book.pdf_url;

        // Générer le slug manuellement
        const bookData = {
            title,
            author,
            // Si aucune langue n'est sélectionnée, on utilise une chaîne vide pour le slug
            // mais 'fr' sera utilisé comme valeur par défaut lors de la mise à jour du livre
            language: language || '',
            publication_year: publication_year || null
        };
        const slug = Book.generateSlug(bookData);

        // Traiter l'image de couverture
        if (req.files && req.files.cover_image_file) {
            try {
                const coverFile = req.files.cover_image_file;

                // Vérifier le type de fichier
                if (!coverFile.mimetype.startsWith('image/')) {
                    req.flash('error', 'Le fichier de couverture doit être une image.');
                    return res.redirect(`/admin/books/edit/${bookId}`);
                }

                // Vérifier la taille du fichier (max 2MB)
                if (coverFile.size > 2 * 1024 * 1024) {
                    req.flash('error', 'L\'image de couverture ne doit pas dépasser 2MB.');
                    return res.redirect(`/admin/books/edit/${bookId}`);
                }

                // Créer un nom de fichier sécurisé basé sur le slug
                const fileManager = await import('../utils/fileManager.js');
                const coverInfo = await fileManager.default.saveCoverImage(coverFile, slug, book.cover_image_url);
                finalCoverImageUrl = coverInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du traitement de l\'image de couverture:', error);
                req.flash('error', 'Une erreur est survenue lors du traitement de l\'image de couverture.');
                return res.redirect(`/admin/books/edit/${bookId}`);
            }
        } else if (cover_image_url && cover_image_url !== book.cover_image_url) {
            // Si une nouvelle URL est fournie, télécharger l'image
            try {
                const fileManager = await import('../utils/fileManager.js');
                const coverInfo = await fileManager.default.downloadCoverImage(cover_image_url, slug, book.cover_image_url);
                finalCoverImageUrl = coverInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du téléchargement de l\'image de couverture:', error);
                // Continuer avec l'URL fournie si le téléchargement échoue
                finalCoverImageUrl = cover_image_url;
            }
        }

        // Traiter le fichier PDF
        if (req.files && req.files.pdf_file) {
            try {
                const pdfFile = req.files.pdf_file;

                // Vérifier le type de fichier
                if (pdfFile.mimetype !== 'application/pdf') {
                    req.flash('error', 'Le fichier doit être au format PDF.');
                    return res.redirect(`/admin/books/edit/${bookId}`);
                }

                // Vérifier la taille du fichier (max 10MB)
                if (pdfFile.size > 10 * 1024 * 1024) {
                    req.flash('error', 'Le fichier PDF ne doit pas dépasser 10MB.');
                    return res.redirect(`/admin/books/edit/${bookId}`);
                }

                // Créer un nom de fichier sécurisé basé sur le slug
                const fileManager = await import('../utils/fileManager.js');
                const pdfInfo = await fileManager.default.saveEbookFile(pdfFile, slug, book.pdf_url);
                finalPdfUrl = pdfInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du traitement du fichier PDF:', error);
                req.flash('error', 'Une erreur est survenue lors du traitement du fichier PDF.');
                return res.redirect(`/admin/books/edit/${bookId}`);
            }
        } else if (pdf_url && pdf_url !== book.pdf_url) {
            // Si une nouvelle URL est fournie, télécharger le PDF
            try {
                const fileManager = await import('../utils/fileManager.js');
                const pdfInfo = await fileManager.default.downloadEbook(pdf_url, slug, book.pdf_url);
                finalPdfUrl = pdfInfo.relativePath;
            } catch (error) {
                console.error('Erreur lors du téléchargement du fichier PDF:', error);
                // Continuer avec l'URL fournie si le téléchargement échoue
                finalPdfUrl = pdf_url;
            }
        }

        // Supprimé car déjà défini plus haut

        // Mettre à jour le livre
        await book.update({
            title,
            author,
            description: description || null,
            summary: summary || null,
            genre_id: finalGenreId || null,
            cover_image_url: finalCoverImageUrl,
            pdf_url: finalPdfUrl,
            publication_year: publication_year || null,
            isbn: isbn || null,
            initial_rating: initial_rating || 0,
            // Ne pas écraser la note moyenne si des avis existent déjà
            // Sinon, utiliser la note initiale comme note moyenne
            average_rating: book.average_rating > 0 ? book.average_rating : (initial_rating || 0),
            language: language || 'fr', // Français par défaut si aucune langue n'est sélectionnée
            status: status || 'draft',
            slug // Ajouter le slug généré
        });

        req.flash('success', `Le livre "${title}" a été mis à jour avec succès.`);
        res.redirect('/admin/books');
    } catch (err) {
        console.error('Erreur lors de la mise à jour du livre:', err);
        req.flash('error', 'Une erreur est survenue lors de la mise à jour du livre.');
        return res.redirect(`/admin/books/edit/${req.params.id}`);
    }
}

/**
 * Supprime un livre
 */
export async function deleteBook(req, res) {
    try {
        const bookId = req.params.id;

        // Récupérer le livre
        const book = await Book.findByPk(bookId);

        if (!book) {
            req.flash('error', 'Le livre demandé n\'existe pas.');
            return res.redirect('/admin/books');
        }

        // Supprimer le livre
        await book.destroy();

        req.flash('success', 'Le livre a été supprimé avec succès.');
        res.redirect('/admin/books');
    } catch (err) {
        console.error('Erreur lors de la suppression du livre:', err);
        req.flash('error', 'Une erreur est survenue lors de la suppression du livre.');
        return res.redirect('/admin/books');
    }
}

/**
 * Affiche la liste des genres pour l'administration
 */
export async function showGenres(req, res) {
    try {
        // Récupérer les paramètres de recherche et de filtre
        const search = req.query.search || '';
        const minBooks = req.query.min_books || '';
        const maxBooks = req.query.max_books || '';
        const sortBy = req.query.sortBy || 'name';
        const sortOrder = req.query.sortOrder || 'ASC';

        // Vérifier si la table des genres existe
        const genreCount = await Genre.count();

        if (genreCount === 0) {
            req.flash('info', 'Aucun genre n\'existe encore. Veuillez d\'abord en créer un.');
            return res.render('admin/genres', {
                title: 'Gestion des genres - Administration - Nookli',
                currentUser: req.user,
                genres: [],
                filters: { search, minBooks, maxBooks, sortBy, sortOrder }
            });
        }

        // Construire les options de requête
        const options = {
            include: [{
                model: Book,
                as: 'books',
                attributes: ['id'],
                required: false
            }],
            order: []
        };

        // Ajouter la recherche par nom
        if (search) {
            options.where = {
                name: {
                    [Op.like]: `%${search}%`
                }
            };
        }

        // Ajouter le tri
        if (sortBy === 'book_count') {
            // Le tri par nombre de livres sera fait en mémoire
            options.order.push(['name', 'ASC']); // Tri par défaut
        } else {
            options.order.push([sortBy, sortOrder]);
        }

        // Récupérer tous les genres avec leurs livres
        let genres = await Genre.findAll(options);

        // Ajouter le nombre de livres à chaque genre
        genres = genres.map(genre => {
            const genreObj = genre.toJSON();
            genreObj.book_count = genre.books ? genre.books.length : 0;
            return genreObj;
        });

        // Filtrer par nombre de livres
        if (minBooks) {
            genres = genres.filter(genre => genre.book_count >= parseInt(minBooks));
        }

        if (maxBooks) {
            genres = genres.filter(genre => genre.book_count <= parseInt(maxBooks));
        }

        // Trier par nombre de livres si nécessaire
        if (sortBy === 'book_count') {
            genres.sort((a, b) => {
                return sortOrder === 'ASC' ?
                    a.book_count - b.book_count :
                    b.book_count - a.book_count;
            });
        }

        res.render('admin/genres', {
            title: 'Gestion des genres - Administration - Nookli',
            currentUser: req.user,
            genres: genres || [],
            filters: { search, minBooks, maxBooks, sortBy, sortOrder }
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des genres:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des genres.');
        res.render('admin/genres', {
            title: 'Gestion des genres - Administration - Nookli',
            currentUser: req.user,
            genres: [],
            filters: {
                search: req.query.search || '',
                minBooks: req.query.min_books || '',
                maxBooks: req.query.max_books || '',
                sortBy: req.query.sortBy || 'name',
                sortOrder: req.query.sortOrder || 'ASC'
            }
        });
    }
}

/**
 * Affiche le formulaire d'ajout de genre
 */
export async function showGenreForm(req, res) {
    res.render('admin/genre-form', {
        title: 'Ajouter un genre - Administration - Nookli',
        currentUser: req.user,
        genre: {},
        formAction: '/admin/genres/new',
        formTitle: 'Ajouter un nouveau genre'
    });
}

/**
 * Crée un nouveau genre
 */
export async function createGenre(req, res) {
    try {
        const { name, description } = req.body;

        // Validation de base
        if (!name) {
            req.flash('error', 'Le nom du genre est obligatoire.');
            return res.redirect('/admin/genres/new');
        }

        // Créer le genre avec Sequelize
        await Genre.create({
            name,
            description: description || null
        });

        req.flash('success', `Le genre "${name}" a été ajouté avec succès.`);
        res.redirect('/admin/genres');
    } catch (err) {
        console.error('Erreur lors de la création du genre:', err);
        req.flash('error', 'Une erreur est survenue lors de la création du genre.');
        return res.redirect('/admin/genres/new');
    }
}

/**
 * Affiche le formulaire de modification d'un genre
 */
export async function showEditGenreForm(req, res) {
    try {
        const genreId = req.params.id;

        // Récupérer le genre avec Sequelize
        const genre = await Genre.findByPk(genreId);

        if (!genre) {
            req.flash('error', 'Le genre demandé n\'existe pas.');
            return res.redirect('/admin/genres');
        }

        res.render('admin/genre-form', {
            title: `Modifier ${genre.name} - Administration - Nookli`,
            currentUser: req.user,
            genre,
            formAction: `/admin/genres/edit/${genreId}`,
            formTitle: 'Modifier le genre'
        });
    } catch (err) {
        console.error('Erreur lors de la récupération du genre:', err);
        req.flash('error', 'Le genre demandé n\'existe pas ou une erreur est survenue.');
        return res.redirect('/admin/genres');
    }
}

/**
 * Met à jour un genre existant
 */
export async function updateGenre(req, res) {
    try {
        const genreId = req.params.id;
        const { name, description } = req.body;

        // Validation de base
        if (!name) {
            req.flash('error', 'Le nom du genre est obligatoire.');
            return res.redirect(`/admin/genres/edit/${genreId}`);
        }

        // Récupérer le genre existant
        const genre = await Genre.findByPk(genreId);

        if (!genre) {
            req.flash('error', 'Le genre demandé n\'existe pas.');
            return res.redirect('/admin/genres');
        }

        // Mettre à jour le genre
        await genre.update({
            name,
            description: description || null
        });

        req.flash('success', `Le genre "${name}" a été mis à jour avec succès.`);
        res.redirect('/admin/genres');
    } catch (err) {
        console.error('Erreur lors de la mise à jour du genre:', err);
        req.flash('error', 'Une erreur est survenue lors de la mise à jour du genre.');
        return res.redirect(`/admin/genres/edit/${req.params.id}`);
    }
}

/**
 * Supprime un genre
 */
export async function deleteGenre(req, res) {
    try {
        const genreId = req.params.id;

        // Récupérer le genre avec ses livres associés
        const genre = await Genre.findByPk(genreId, {
            include: [{
                model: Book,
                as: 'books',
                attributes: ['id']
            }]
        });

        if (!genre) {
            req.flash('error', 'Le genre demandé n\'existe pas.');
            return res.redirect('/admin/genres');
        }

        // Vérifier si des livres utilisent ce genre
        if (genre.books && genre.books.length > 0) {
            req.flash('error', `Ce genre est utilisé par ${genre.books.length} livre(s). Veuillez d'abord modifier ces livres.`);
            return res.redirect('/admin/genres');
        }

        // Supprimer le genre
        await genre.destroy();

        req.flash('success', 'Le genre a été supprimé avec succès.');
        res.redirect('/admin/genres');
    } catch (err) {
        console.error('Erreur lors de la suppression du genre:', err);
        req.flash('error', 'Une erreur est survenue lors de la suppression du genre.');
        return res.redirect('/admin/genres');
    }
}

/**
 * Affiche la liste des utilisateurs pour l'administration
 */
export async function showUsers(req, res) {
    try {
        // Récupérer les paramètres de recherche et de filtre
        const search = req.query.search || '';
        const role = req.query.role || '';
        const sortBy = req.query.sortBy || 'username';
        const sortOrder = req.query.sortOrder || 'ASC';

        // Construire les options de requête
        const options = {
            order: [[sortBy, sortOrder]],
            attributes: { exclude: ['password'] } // Ne pas inclure le mot de passe
        };

        // Ajouter les filtres
        const whereConditions = {};

        if (search) {
            whereConditions[Op.or] = [
                { username: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } }
            ];
        }

        if (role) {
            whereConditions.role = role;
        }

        if (Object.keys(whereConditions).length > 0) {
            options.where = whereConditions;
        }

        // Récupérer les utilisateurs
        const users = await User.findAll(options);

        res.render('admin/users', {
            title: 'Gestion des utilisateurs - Administration - Nookli',
            currentUser: req.user,
            users: users || [],
            filters: { search, role, sortBy, sortOrder }
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des utilisateurs:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des utilisateurs.');
        res.render('admin/users', {
            title: 'Gestion des utilisateurs - Administration - Nookli',
            currentUser: req.user,
            users: [],
            filters: {
                search: req.query.search || '',
                role: req.query.role || '',
                sortBy: req.query.sortBy || 'username',
                sortOrder: req.query.sortOrder || 'ASC'
            }
        });
    }
}

/**
 * Affiche le formulaire de modification d'un utilisateur
 */
export async function showEditUserForm(req, res) {
    try {
        const userId = req.params.id;

        // Récupérer l'utilisateur avec Sequelize
        const user = await User.findByPk(userId, {
            attributes: { exclude: ['password'] } // Ne pas inclure le mot de passe
        });

        if (!user) {
            req.flash('error', 'L\'utilisateur demandé n\'existe pas.');
            return res.redirect('/admin/users');
        }

        res.render('admin/user-form', {
            title: `Modifier ${user.username} - Administration - Nookli`,
            currentUser: req.user,
            user,
            formAction: `/admin/users/edit/${userId}`,
            formTitle: 'Modifier l\'utilisateur'
        });
    } catch (err) {
        console.error('Erreur lors de la récupération de l\'utilisateur:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement de l\'utilisateur.');
        res.redirect('/admin/users');
    }
}

/**
 * Met à jour un utilisateur existant
 */
export async function updateUser(req, res) {
    try {
        const userId = req.params.id;
        const { username, email, role, password } = req.body;

        // Validation de base
        if (!username || !email) {
            req.flash('error', 'Le nom d\'utilisateur et l\'email sont obligatoires.');
            return res.redirect(`/admin/users/edit/${userId}`);
        }

        // Récupérer l'utilisateur existant
        const user = await User.findByPk(userId);

        if (!user) {
            req.flash('error', 'L\'utilisateur demandé n\'existe pas.');
            return res.redirect('/admin/users');
        }

        // Préparer les données de mise à jour
        const userData = {
            username,
            email,
            role: role || 'reader'
        };

        // Ajouter le mot de passe uniquement s'il est fourni
        if (password && password.trim() !== '') {
            userData.password = password;
        }

        // Mettre à jour l'utilisateur
        await user.update(userData);

        req.flash('success', `L'utilisateur "${username}" a été mis à jour avec succès.`);
        res.redirect('/admin/users');
    } catch (err) {
        console.error('Erreur lors de la mise à jour de l\'utilisateur:', err);
        req.flash('error', 'Une erreur est survenue lors de la mise à jour de l\'utilisateur.');
        res.redirect(`/admin/users/edit/${req.params.id}`);
    }
}

/**
 * Supprime un utilisateur
 */
export async function deleteUser(req, res) {
    try {
        const userId = req.params.id;

        // Empêcher la suppression de son propre compte
        if (parseInt(userId) === req.user.id) {
            req.flash('error', 'Vous ne pouvez pas supprimer votre propre compte.');
            return res.redirect('/admin/users');
        }

        // Récupérer l'utilisateur
        const user = await User.findByPk(userId);

        if (!user) {
            req.flash('error', 'L\'utilisateur demandé n\'existe pas.');
            return res.redirect('/admin/users');
        }

        // Supprimer l'utilisateur
        await user.destroy();

        req.flash('success', 'L\'utilisateur a été supprimé avec succès.');
        res.redirect('/admin/users');
    } catch (err) {
        console.error('Erreur lors de la suppression de l\'utilisateur:', err);
        req.flash('error', 'Une erreur est survenue lors de la suppression de l\'utilisateur.');
        return res.redirect('/admin/users');
    }
}

/**
 * Affiche les statistiques du site
 */
export async function showStats(req, res) {
    try {
        // Récupérer les statistiques
        const [userCount, adminCount, bookCount, genreCount] = await Promise.all([
            // Nombre total d'utilisateurs
            User.count(),
            // Nombre d'administrateurs
            User.count({ where: { role: 'admin' } }),
            // Nombre total de livres
            Book.count(),
            // Nombre total de genres
            Genre.count()
        ]);

        const stats = [
            { label: 'Utilisateurs', count: userCount },
            { label: 'Administrateurs', count: adminCount },
            { label: 'Livres', count: bookCount },
            { label: 'Genres', count: genreCount }
        ];

        res.render('admin/stats', {
            title: 'Statistiques - Administration - Nookli',
            currentUser: req.user,
            stats
        });
    } catch (err) {
        console.error('Erreur lors de la récupération des statistiques:', err);
        req.flash('error', 'Une erreur est survenue lors du chargement des statistiques.');
        res.render('admin/stats', {
            title: 'Statistiques - Administration - Nookli',
            currentUser: req.user,
            stats: []
        });
    }
}

// Exporter toutes les fonctions
export default {
    showDashboard,
    showBooks,
    showBookForm,
    createBook,
    showEditBookForm,
    updateBook,
    deleteBook,
    showGenres,
    showGenreForm,
    createGenre,
    showEditGenreForm,
    updateGenre,
    deleteGenre,
    showUsers,
    showEditUserForm,
    updateUser,
    deleteUser,
    showStats
};