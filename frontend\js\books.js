// JavaScript pour les fonctionnalités des pages de livres

document.addEventListener('DOMContentLoaded', function() {
    // Gestion de l'affichage des filtres sur mobile
    const toggleFiltersBtn = document.querySelector('.toggle-filters');
    const filtersContainer = document.querySelector('.filters-container');
    
    if (toggleFiltersBtn && filtersContainer) {
        toggleFiltersBtn.addEventListener('click', function() {
            filtersContainer.classList.toggle('show');
            toggleFiltersBtn.classList.toggle('active');
        });
    }
    
    // Gestion des images de couverture qui ne se chargent pas
    const coverImages = document.querySelectorAll('.book-cover img');
    
    coverImages.forEach(img => {
        img.addEventListener('error', function() {
            // Remplacer par une image par défaut en cas d'erreur
            this.src = 'https://via.placeholder.com/300x450?text=Nookli';
        });
    });
    
    // Animation des cartes de livres au scroll
    const bookCards = document.querySelectorAll('.book-card');
    
    if ('IntersectionObserver' in window) {
        const bookObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    bookObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });
        
        bookCards.forEach(card => {
            card.classList.add('fade-in');
            bookObserver.observe(card);
        });
    } else {
        // Fallback pour les navigateurs qui ne supportent pas IntersectionObserver
        bookCards.forEach(card => {
            card.classList.add('visible');
        });
    }
});
