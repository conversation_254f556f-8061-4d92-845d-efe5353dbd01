/* Styles pour les formulaires */
.reader-form-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.reader-form .form-group {
    margin-bottom: 1.5rem;
}

.reader-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.reader-form input[type="text"],
.reader-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.reader-form input[type="text"]:focus,
.reader-form textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.reader-form small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.85rem;
}

.reader-form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
}

/* Responsive */
@media (max-width: 768px) {
    .reader-form-container {
        padding: 1.5rem;
    }
    
    .reader-form-actions {
        flex-direction: column;
    }
    
    .reader-form-actions .btn {
        width: 100%;
    }
}