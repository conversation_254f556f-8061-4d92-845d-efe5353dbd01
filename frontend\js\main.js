// main.js - Point d'entrée JavaScript côté client
import ReadingListsManager from './components/reading-lists.js';
import FavoritesManager from './components/favorites.js';
import ReviewsManager from './components/reviews.js';

document.addEventListener('DOMContentLoaded', () => {
    console.log('Nookli - Application chargée');

    // Fonction pour gérer les formulaires de recherche
    setupSearchForm();

    // Fonction pour gérer le menu mobile
    setupMobileMenu();

    // Fonction pour gérer les messages flash
    setupFlashMessages();

    // Initialiser les composants de fonctionnalités lecteur
    initReaderFeatures();
});

/**
 * Configure le formulaire de recherche
 */
function setupSearchForm() {
    const searchForms = document.querySelectorAll('.search-form, .search-form-hero');

    searchForms.forEach(form => {
        form.addEventListener('submit', (e) => {
            const input = form.querySelector('input[type="search"]');

            // Validation simple: empêcher la soumission si le champ est vide
            if (!input.value.trim()) {
                e.preventDefault();
                input.classList.add('error');

                // Retirer la classe d'erreur quand l'utilisateur commence à taper
                input.addEventListener('input', () => {
                    input.classList.remove('error');
                }, { once: true });
            }
        });
    });
}

/**
 * Configure le menu mobile
 */
function setupMobileMenu() {
    const menuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    if (menuToggle && navLinks) {
        menuToggle.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });

        // Fermer le menu quand on clique sur un lien
        const links = navLinks.querySelectorAll('a');
        links.forEach(link => {
            link.addEventListener('click', () => {
                navLinks.classList.remove('active');
            });
        });
    }
}

/**
 * Configure les messages flash pour qu'ils disparaissent automatiquement
 */
function setupFlashMessages() {
    const alerts = document.querySelectorAll('.alert');

    if (alerts.length > 0) {
        // Faire disparaître les messages après 5 secondes
        setTimeout(() => {
            alerts.forEach(alert => {
                // Animation de disparition
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';

                // Supprimer l'élément après l'animation
                setTimeout(() => {
                    alert.remove();
                }, 500);
            });
        }, 5000);

        // Permettre à l'utilisateur de fermer les messages en cliquant dessus
        alerts.forEach(alert => {
            alert.style.cursor = 'pointer';
            alert.title = 'Cliquer pour fermer';

            alert.addEventListener('click', () => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    alert.remove();
                }, 500);
            });
        });
    }
}

/**
 * Fonction utilitaire pour les appels API
 * @param {string} url - URL de l'API
 * @param {Object} options - Options fetch
 * @returns {Promise} - Promesse avec les données JSON
 */
async function fetchAPI(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json'
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

/**
 * Initialise les composants de fonctionnalités lecteur
 */
function initReaderFeatures() {
    // Vérifier si l'utilisateur est connecté en vérifiant la présence du dashboard reader
    const isDashboard = document.querySelector('.reader-sidebar') !== null ||
                       document.querySelector('.reader-container') !== null ||
                       document.querySelector('.reading-lists-container') !== null;

    // Vérifier si on est sur une page de détails de livre
    const isBookDetails = document.querySelector('.book-details') !== null;

    // Vérifier si l'utilisateur est connecté sur la page de détails
    const hasUserActions = document.querySelector('.book-actions') !== null;
    const hasGuestActions = document.querySelector('.book-actions-guest') !== null;

    console.log('État de connexion:', { isDashboard, isBookDetails, hasUserActions, hasGuestActions });
    console.log('Éléments trouvés:', {
        'book-actions': document.querySelector('.book-actions'),
        'book-actions-guest': document.querySelector('.book-actions-guest'),
        'book-details': document.querySelector('.book-details')
    });

    if (isDashboard) {
        // Sur le dashboard, l'utilisateur est forcément connecté
        console.log('Initialisation des fonctionnalités lecteur (dashboard)...');

        ReadingListsManager.init();
        FavoritesManager.init();
        ReviewsManager.init();
    } else if (isBookDetails) {
        if (hasUserActions && !hasGuestActions) {
            // Utilisateur connecté sur la page de détails
            console.log('Initialisation des fonctionnalités lecteur (livre - connecté)...');

            ReadingListsManager.init();
            FavoritesManager.init();
            ReviewsManager.init();
            initBookDetailsPage();
        } else {
            // Utilisateur non connecté sur la page de détails
            console.log('Initialisation des avis seulement (livre - non connecté)...');
            ReviewsManager.init();
        }
    }
}

/**
 * Initialise spécifiquement les fonctionnalités sur la page de détails du livre
 */
function initBookDetailsPage() {
    // Vérifier si on est sur la page de détails d'un livre
    const bookDetailsContainer = document.querySelector('.book-details');
    if (!bookDetailsContainer) return;

    console.log('Initialisation de la page de détails du livre...');

    // Initialiser les composants spécifiquement pour cette page
    if (FavoritesManager && typeof FavoritesManager.initBookDetailsPage === 'function') {
        FavoritesManager.initBookDetailsPage();
    }

    if (ReadingListsManager && typeof ReadingListsManager.initBookDetailsPage === 'function') {
        ReadingListsManager.initBookDetailsPage();
    }

    if (ReviewsManager && typeof ReviewsManager.initBookDetailsPage === 'function') {
        ReviewsManager.initBookDetailsPage();
    }
}