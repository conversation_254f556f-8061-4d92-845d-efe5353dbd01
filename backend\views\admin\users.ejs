<%- include('layout', {
    path: '/admin/users',
    success: typeof success !== 'undefined' ? success : '',
    error: typeof error !== 'undefined' ? error : '',
    info: typeof info !== 'undefined' ? info : '',
    body: `
<div class="admin-page-header">
    <h1>Gestion des utilisateurs</h1>
</div>

<!-- Barre de recherche et filtres -->
<div class="admin-filters">
    <div class="admin-filters-header">
        <h3>Recherche et filtres</h3>
        <button class="admin-filters-toggle"><i class="fas fa-filter"></i> Afficher les filtres</button>
    </div>

    <form class="admin-search-bar" method="GET" action="/admin/users">
        <input type="text" name="search" class="admin-search-input" placeholder="Rechercher par nom d'utilisateur ou email..." value="${filters ? filters.search : ''}">
        <button type="submit" class="admin-search-button"><i class="fas fa-search"></i></button>
    </form>

    <div class="admin-filters-content">
        <div class="admin-filter-group">
            <label class="admin-filter-label" for="role">Rôle</label>
            <select name="role" id="role" class="admin-filter-select">
                <option value="">Tous les rôles</option>
                <option value="admin" ${filters && filters.role === 'admin' ? 'selected' : ''}>Administrateur</option>
                <option value="reader" ${filters && filters.role === 'reader' ? 'selected' : ''}>Lecteur</option>
            </select>
        </div>

        <div class="admin-filter-group">
            <label class="admin-filter-label" for="sortBy">Trier par</label>
            <select name="sortBy" id="sortBy" class="admin-filter-select">
                <option value="username" ${filters && filters.sortBy === 'username' ? 'selected' : ''}>Nom d'utilisateur</option>
                <option value="email" ${filters && filters.sortBy === 'email' ? 'selected' : ''}>Email</option>
                <option value="created_at" ${filters && filters.sortBy === 'created_at' ? 'selected' : ''}>Date d'inscription</option>
            </select>
        </div>

        <div class="admin-filter-group">
            <label class="admin-filter-label" for="sortOrder">Ordre</label>
            <select name="sortOrder" id="sortOrder" class="admin-filter-select">
                <option value="ASC" ${filters && filters.sortOrder === 'ASC' ? 'selected' : ''}>Croissant</option>
                <option value="DESC" ${filters && filters.sortOrder === 'DESC' ? 'selected' : ''}>Décroissant</option>
            </select>
        </div>

        <div class="admin-filters-actions">
            <button type="reset" class="admin-filter-reset">Réinitialiser</button>
            <button type="submit" class="admin-filter-apply">Appliquer</button>
        </div>
    </div>
</div>

<!-- Vue tableau pour desktop -->
<div class="admin-table-container">
    <table class="admin-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nom d'utilisateur</th>
                <th>Email</th>
                <th>Rôle</th>
                <th>Date d'inscription</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            ${users.length === 0 ? `
                <tr>
                    <td colspan="6" class="admin-table-empty">Aucun utilisateur trouvé.</td>
                </tr>
            ` : ''}

            ${users.map(user => `
                <tr>
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td>
                        <span class="admin-badge ${user.role === 'admin' ? 'admin-badge-primary' : 'admin-badge-secondary'}">
                            ${user.role === 'admin' ? 'Administrateur' : 'Lecteur'}
                        </span>
                    </td>
                    <td>${new Date(user.created_at).toLocaleDateString()}</td>
                    <td>
                        <div class="admin-actions">
                            <a href="/admin/users/edit/${user.id}" class="admin-action-btn edit" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </a>
                            ${user.id !== currentUser.id ? `
                                <button class="admin-action-btn delete" title="Supprimer"
                                        onclick="confirmDelete('${user.username.replace(/'/g, "\\'")}', '/admin/users/delete/${user.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `).join('')}
        </tbody>
    </table>
</div>

<!-- Vue en liste pour mobile -->
<div class="admin-list-view">
    ${users.length === 0 ? `
        <div class="admin-card">
            <div class="admin-card-body admin-table-empty">
                Aucun utilisateur trouvé.
            </div>
        </div>
    ` : ''}

    ${users.map(user => `
        <div class="admin-card admin-user-card ${user.role === 'admin' ? 'admin-role' : 'reader-role'}" id="user-card-${user.id}">
            <div class="admin-card-header">
                <span>Utilisateur #${user.id}</span>
            </div>
            <div class="admin-card-body">
                <div class="admin-user-card-header">
                    <div class="admin-user-card-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="admin-user-card-info">
                        <div class="admin-user-card-name">${user.username}</div>
                        <div class="admin-user-card-email">${user.email}</div>
                    </div>
                </div>

                <div class="admin-card-field">
                    <div class="admin-card-label">Rôle</div>
                    <div class="admin-card-value">
                        <span class="admin-badge ${user.role === 'admin' ? 'admin-badge-primary' : 'admin-badge-secondary'}">
                            ${user.role === 'admin' ? 'Administrateur' : 'Lecteur'}
                        </span>
                    </div>
                </div>

                <div class="admin-card-field">
                    <div class="admin-card-label">Inscription</div>
                    <div class="admin-card-value">${new Date(user.created_at).toLocaleDateString()}</div>
                </div>

                <div class="admin-card-actions">
                    <a href="/admin/users/edit/${user.id}" class="btn btn-sm btn-primary btn-xs">
                        <i class="fas fa-edit"></i>
                    </a>
                    ${user.id !== currentUser.id ? `
                        <button class="btn btn-sm btn-danger btn-xs"
                                onclick="confirmDelete('${user.username.replace(/'/g, "\\'")}', '/admin/users/delete/${user.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('')}
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="admin-modal">
    <div class="admin-modal-content">
        <h2>Confirmer la suppression</h2>
        <p id="deleteMessage">Êtes-vous sûr de vouloir supprimer cet élément ?</p>
        <div class="admin-modal-actions">
            <button class="btn btn-secondary" onclick="closeModal()">Annuler</button>
            <form id="deleteForm" method="POST">
                <button type="submit" class="btn btn-danger">Supprimer</button>
            </form>
        </div>
    </div>
</div>

<script>
    function confirmDelete(username, deleteUrl) {
        document.getElementById('deleteMessage').textContent = \`Êtes-vous sûr de vouloir supprimer l'utilisateur "\${username}" ? Cette action est irréversible.\`;
        document.getElementById('deleteForm').action = deleteUrl;
        document.getElementById('deleteModal').style.display = 'flex';
    }

    function closeModal() {
        document.getElementById('deleteModal').style.display = 'none';
    }

    // Fermer la modal si on clique en dehors
    window.onclick = function(event) {
        const modal = document.getElementById('deleteModal');
        if (event.target === modal) {
            closeModal();
        }
    }
</script>
` }) %>
