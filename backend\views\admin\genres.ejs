<%- include('layout', {
    path: '/admin/genres',
    success: typeof success !== 'undefined' ? success : '',
    error: typeof error !== 'undefined' ? error : '',
    info: typeof info !== 'undefined' ? info : '',
    body: `
<div class="admin-page-header">
    <h1>Gestion des genres</h1>
    <a href="/admin/genres/new" class="btn btn-primary">
        <i class="fas fa-plus"></i> Ajouter un genre
    </a>
</div>

<!-- Barre de recherche et filtres -->
<div class="admin-filters">
    <div class="admin-filters-header">
        <h3>Recherche et filtres</h3>
        <button class="admin-filters-toggle"><i class="fas fa-filter"></i> Afficher les filtres</button>
    </div>

    <form class="admin-search-bar" method="GET" action="/admin/genres">
        <input type="text" name="search" class="admin-search-input" placeholder="Rechercher par nom de genre..." value="${filters ? filters.search : ''}">
        <button type="submit" class="admin-search-button"><i class="fas fa-search"></i></button>
    </form>

    <div class="admin-filters-content">
        <div class="admin-filter-group">
            <label class="admin-filter-label" for="min_books">Nombre minimum de livres</label>
            <input type="number" name="min_books" id="min_books" class="admin-filter-input" min="0" placeholder="Ex: 5" value="${filters && filters.minBooks ? filters.minBooks : ''}">
        </div>

        <div class="admin-filter-group">
            <label class="admin-filter-label" for="max_books">Nombre maximum de livres</label>
            <input type="number" name="max_books" id="max_books" class="admin-filter-input" min="0" placeholder="Ex: 20" value="${filters && filters.maxBooks ? filters.maxBooks : ''}">
        </div>

        <div class="admin-filter-group">
            <label class="admin-filter-label" for="sortBy">Trier par</label>
            <select name="sortBy" id="sortBy" class="admin-filter-select">
                <option value="name" ${filters && filters.sortBy === 'name' ? 'selected' : ''}>Nom</option>
                <option value="book_count" ${filters && filters.sortBy === 'book_count' ? 'selected' : ''}>Nombre de livres</option>
                <option value="created_at" ${filters && filters.sortBy === 'created_at' ? 'selected' : ''}>Date de création</option>
            </select>
        </div>

        <div class="admin-filter-group">
            <label class="admin-filter-label" for="sortOrder">Ordre</label>
            <select name="sortOrder" id="sortOrder" class="admin-filter-select">
                <option value="ASC" ${filters && filters.sortOrder === 'ASC' ? 'selected' : ''}>Croissant</option>
                <option value="DESC" ${filters && filters.sortOrder === 'DESC' ? 'selected' : ''}>Décroissant</option>
            </select>
        </div>

        <div class="admin-filters-actions">
            <button type="reset" class="admin-filter-reset">Réinitialiser</button>
            <button type="submit" class="admin-filter-apply">Appliquer</button>
        </div>
    </div>
</div>

<!-- Vue tableau pour desktop -->
<div class="admin-table-container">
    <table class="admin-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nom</th>
                <th>Description</th>
                <th>Livres</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            ${genres.length === 0 ? `
                <tr>
                    <td colspan="5" class="admin-table-empty">Aucun genre trouvé. <a href="/admin/genres/new">Ajouter un genre</a></td>
                </tr>
            ` : ''}

            ${genres.map(genre => `
                <tr>
                    <td>${genre.id}</td>
                    <td>${genre.name}</td>
                    <td>${genre.description || '<em>Aucune description</em>'}</td>
                    <td>${genre.book_count}</td>
                    <td>
                        <div class="admin-actions">
                            <a href="/admin/genres/edit/${genre.id}" class="admin-action-btn edit" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="admin-action-btn delete" title="Supprimer"
                                    onclick="confirmDelete('${genre.name.replace(/'/g, "\\'")}', ${genre.book_count}, '/admin/genres/delete/${genre.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('')}
        </tbody>
    </table>
</div>

<!-- Vue en liste pour mobile -->
<div class="admin-list-view">
    ${genres.length === 0 ? `
        <div class="admin-card">
            <div class="admin-card-body admin-table-empty">
                Aucun genre trouvé. <a href="/admin/genres/new">Ajouter un genre</a>
            </div>
        </div>
    ` : ''}

    ${genres.map(genre => `
        <div class="admin-card" id="genre-card-${genre.id}">
            <div class="admin-card-header">
                <span>${genre.name}</span>
                <span class="admin-genre-card-count">${genre.book_count} livre${genre.book_count !== 1 ? 's' : ''}</span>
            </div>
            <div class="admin-card-body">
                <div class="admin-genre-card-description">
                    ${genre.description || '<em>Aucune description</em>'}
                </div>

                <div class="admin-card-actions">
                    <a href="/admin/genres/edit/${genre.id}" class="btn btn-sm btn-primary btn-xs">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button class="btn btn-sm btn-danger btn-xs"
                            onclick="confirmDelete('${genre.name.replace(/'/g, "\\'")}', ${genre.book_count}, '/admin/genres/delete/${genre.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('')}
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="admin-modal">
    <div class="admin-modal-content">
        <h2>Confirmer la suppression</h2>
        <p id="deleteMessage">Êtes-vous sûr de vouloir supprimer cet élément ?</p>
        <div class="admin-modal-actions">
            <button class="btn btn-secondary" onclick="closeModal()">Annuler</button>
            <form id="deleteForm" method="POST">
                <button type="submit" class="btn btn-danger">Supprimer</button>
            </form>
        </div>
    </div>
</div>

<script>
    function confirmDelete(name, bookCount, deleteUrl) {
        if (bookCount > 0) {
            document.getElementById('deleteMessage').innerHTML = \`
                <strong>Impossible de supprimer le genre "\${name}"</strong><br>
                Ce genre est utilisé par \${bookCount} livre(s). Veuillez d'abord modifier ces livres.
            \`;
            document.getElementById('deleteForm').style.display = 'none';
            document.getElementById('deleteModal').style.display = 'flex';
            return;
        }

        document.getElementById('deleteMessage').textContent = \`Êtes-vous sûr de vouloir supprimer le genre "\${name}" ?\`;
        document.getElementById('deleteForm').style.display = 'block';
        document.getElementById('deleteForm').action = deleteUrl;
        document.getElementById('deleteModal').style.display = 'flex';
    }

    function closeModal() {
        document.getElementById('deleteModal').style.display = 'none';
    }

    // Fermer la modal si on clique en dehors
    window.onclick = function(event) {
        const modal = document.getElementById('deleteModal');
        if (event.target === modal) {
            closeModal();
        }
    }
</script>
` }) %>
