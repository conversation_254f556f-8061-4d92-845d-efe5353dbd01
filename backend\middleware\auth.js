/**
 * Middleware pour vérifier si l'utilisateur est authentifié
 * Redirige vers la page de connexion si non authentifié (pour les pages web)
 * Retourne une erreur JSON si non authentifié (pour les appels API)
 */
export function isAuthenticated(req, res, next) {
    if (req.isAuthenticated()) {
        return next();
    }

    // Vérifier si c'est une requête API
    const isApiRequest = req.originalUrl.startsWith('/api/') ||
                        req.xhr ||
                        req.headers.accept?.includes('application/json');

    if (isApiRequest) {
        // Pour les requêtes API, retourner une erreur JSON
        return res.status(401).json({
            success: false,
            message: 'Vous devez être connecté pour accéder à cette ressource'
        });
    }

    // Pour les pages web, rediriger vers la page de connexion
    // Stocker l'URL d'origine pour rediriger après connexion
    req.session.returnTo = req.originalUrl;

    // Message flash pour informer l'utilisateur
    req.flash('error', 'Vous devez être connecté pour accéder à cette page');

    // Rediriger vers la page de connexion
    res.redirect('/login');
}

/**
 * Middleware pour vérifier si l'utilisateur est un administrateur
 * Renvoie une erreur 403 si l'utilisateur n'est pas admin
 */
export function isAdmin(req, res, next) {
    if (req.isAuthenticated() && req.user.role === 'admin') {
        return next();
    }

    // Message flash pour informer l'utilisateur
    req.flash('error', 'Accès refusé. Vous devez être administrateur pour accéder à cette page');

    // Rediriger vers la page d'accueil avec un statut 403
    res.status(403).redirect('/');
}

/**
 * Middleware pour vérifier si l'utilisateur n'est PAS authentifié
 * Utile pour les pages de connexion/inscription
 * Redirige vers le tableau de bord ou l'admin selon le rôle de l'utilisateur
 */
export function isNotAuthenticated(req, res, next) {
    if (!req.isAuthenticated()) {
        return next();
    }

    // Rediriger selon le rôle de l'utilisateur
    if (req.user.role === 'admin') {
        // Rediriger les administrateurs vers l'interface d'administration
        return res.redirect('/admin');
    } else {
        // Rediriger les lecteurs vers leur tableau de bord
        return res.redirect('/dashboard');
    }
}
