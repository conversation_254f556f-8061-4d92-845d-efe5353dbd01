/**
 * Gestionnaire de serveur
 * Ce module contient les fonctions pour gérer le démarrage du serveur
 */

import { initDatabase } from '../config/db.js';
import { checkAndCreateAdmin } from './adminManager.js';

/**
 * Initialise le serveur
 * @param {Object} models - Les modèles Sequelize
 * @returns {Promise<boolean>} - true si l'initialisation est réussie, false sinon
 */
export const initServer = async (models) => {
  try {
    // Initialiser la base de données
    const dbInitialized = await initDatabase(models);
    if (!dbInitialized) {
      console.error('[SERVER] Erreur lors de l\'initialisation de la base de données.');
      return false;
    }

    // Vérifier et créer un administrateur si nécessaire
    const adminCreated = await checkAndCreateAdmin(models.User);
    if (!adminCreated) {
      console.error('[SERVER] Erreur lors de la vérification/création de l\'administrateur.');
      return false;
    }

    return true;
  } catch (error) {
    console.error('[SERVER] Erreur lors de l\'initialisation du serveur:', error);
    return false;
  }
};

/**
 * Démarre le serveur
 * @param {Object} app - L'application Express
 * @param {number} port - Le port sur lequel démarrer le serveur
 * @returns {Promise<boolean>} - true si le démarrage est réussi, false sinon
 */
export const startServer = async (app, port) => {
  return new Promise((resolve) => {
    app.listen(port, () => {
      console.log(`[SERVER] Serveur Nookli démarré et à l'écoute sur http://localhost:${port}`);
      resolve(true);
    });
  });
};

export default {
  initServer,
  startServer
};
