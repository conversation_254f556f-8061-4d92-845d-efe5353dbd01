import express from 'express';
import * as bookController from '../controllers/bookController.js';

const router = express.Router();

// Affichage de tous les livres avec options de filtrage et tri
router.get('/', bookController.showAllBooks);

// Recherche de livres par titre ou auteur
router.get('/search', bookController.searchBooks);

// Filtrage des livres par genre
router.get('/genre/:id', bookController.showBooksByGenre);

// Affichage des détails d'un livre (doit être après les routes spécifiques)
router.get('/:id', bookController.showBookDetails);

export default router;
