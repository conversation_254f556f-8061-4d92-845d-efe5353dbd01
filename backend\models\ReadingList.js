import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/db.js';

class ReadingList extends Model {
  /**
   * Crée les listes de lecture par défaut pour un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @returns {Promise<Array>} - Les listes créées
   */
  static async createDefaultLists(userId) {
    try {
      console.log(`Création des listes de lecture par défaut pour l'utilisateur ${userId}...`);

      const defaultLists = [
        { user_id: userId, list_name: 'À lire', is_default: true },
        { user_id: userId, list_name: 'En cours', is_default: true },
        { user_id: userId, list_name: '<PERSON>', is_default: true }
      ];

      const createdLists = await Promise.all(defaultLists.map(list => ReadingList.create(list)));
      console.log(`${createdLists.length} listes par défaut créées pour l'utilisateur ${userId}`);

      return createdLists;
    } catch (error) {
      console.error('Erreur lors de la création des listes par défaut:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON> toutes les listes de lecture d'un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @returns {Promise<Array>} - Les listes de lecture
   */
  static async findByUserId(userId) {
    try {
      return await ReadingList.findAll({
        where: { user_id: userId },
        order: [
          ['is_default', 'DESC'], // Listes par défaut d'abord
          ['list_name', 'ASC']    // Puis par ordre alphabétique
        ]
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des listes de lecture:', error);
      throw error;
    }
  }

  /**
   * Récupère une liste de lecture par son ID et l'ID de l'utilisateur
   * @param {number} listId - ID de la liste
   * @param {number} userId - ID de l'utilisateur
   * @returns {Promise<ReadingList|null>} - La liste trouvée ou null
   */
  static async findByIdAndUserId(listId, userId) {
    try {
      return await ReadingList.findOne({
        where: {
          id: listId,
          user_id: userId
        }
      });
    } catch (error) {
      console.error('Erreur lors de la récupération de la liste de lecture:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les listes de lecture d'un utilisateur avec les livres associés
   * @param {number} userId - ID de l'utilisateur
   * @param {Object} options - Options de pagination et filtrage
   * @returns {Promise<Array>} - Les listes de lecture avec leurs livres
   */
  static async findByUserIdWithBooks(userId, options = {}) {
    try {
      console.log(`Récupération des listes de lecture pour l'utilisateur ${userId}...`);

      const { search = '', limit = null, page = 1, pageSize = 20 } = options;

      // Calculer l'offset pour la pagination
      const offset = (page - 1) * pageSize;

      // Optimisation: Vérifier d'abord si l'utilisateur a des listes
      const listCount = await ReadingList.count({
        where: { user_id: userId }
      });

      console.log(`Nombre de listes trouvées pour l'utilisateur ${userId}: ${listCount}`);

      if (listCount === 0) {
        console.log(`Aucune liste trouvée pour l'utilisateur ${userId}`);
        return [];
      }

      // Configuration de base de la requête
      const queryOptions = {
        where: { user_id: userId },
        order: [
          ['is_default', 'DESC'],
          ['list_name', 'ASC']
        ]
      };

      // Optimisation: Si une recherche est spécifiée, utiliser une approche différente
      if (search) {
        // Récupérer d'abord les IDs des livres qui correspondent à la recherche
        const bookIds = await sequelize.models.Book.findAll({
          attributes: ['id'],
          where: {
            [sequelize.Op.or]: [
              { title: { [sequelize.Op.like]: `%${search}%` } },
              { author: { [sequelize.Op.like]: `%${search}%` } }
            ]
          }
        }).then(books => books.map(book => book.id));

        // Si aucun livre ne correspond, retourner les listes sans livres
        if (bookIds.length === 0) {
          return await ReadingList.findAll({
            where: { user_id: userId },
            include: [
              {
                model: sequelize.models.Book,
                as: 'books',
                through: { attributes: [] },
                required: false
              }
            ],
            order: [
              ['is_default', 'DESC'],
              ['list_name', 'ASC']
            ]
          });
        }

        // Récupérer les listes avec les livres filtrés
        return await ReadingList.findAll({
          where: { user_id: userId },
          include: [
            {
              model: sequelize.models.Book,
              as: 'books',
              through: { attributes: [] },
              where: { id: { [sequelize.Op.in]: bookIds } },
              required: false,
              include: [
                {
                  model: sequelize.models.Genre,
                  as: 'genre',
                  attributes: ['id', 'name']
                }
              ]
            }
          ],
          order: [
            ['is_default', 'DESC'],
            ['list_name', 'ASC'],
            [{ model: sequelize.models.Book, as: 'books' }, 'title', 'ASC']
          ]
        });
      }

      // Ajouter les options de pagination si nécessaire
      if (limit && Number.isInteger(limit) && limit > 0) {
        queryOptions.limit = limit;
      } else if (pageSize) {
        queryOptions.limit = pageSize;
        queryOptions.offset = offset;
      }

      // Inclure les relations nécessaires
      queryOptions.include = [
        {
          model: sequelize.models.Book,
          as: 'books',
          through: { attributes: [] },
          include: [
            {
              model: sequelize.models.Genre,
              as: 'genre',
              attributes: ['id', 'name']
            }
          ]
        }
      ];

      // Ajouter l'ordre de tri pour les livres
      queryOptions.order.push([{ model: sequelize.models.Book, as: 'books' }, 'title', 'ASC']);

      // Exécuter la requête optimisée
      const lists = await ReadingList.findAll(queryOptions);
      console.log(`${lists.length} listes récupérées pour l'utilisateur ${userId}`);

      return lists;
    } catch (error) {
      console.error('Erreur lors de la récupération des listes avec livres:', error);
      throw error;
    }
  }
}

// Définition du modèle
ReadingList.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    list_name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    sequelize,
    modelName: 'ReadingList',
    tableName: 'READING_LISTS',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'list_name']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['is_default']
      }
    ]
  }
);

export default ReadingList;
