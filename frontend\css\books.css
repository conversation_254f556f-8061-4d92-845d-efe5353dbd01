/* Styles pour les pages de livres */

/* Grille de livres - Mobile First */
.books-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2 colonnes sur mobile */
    gap: 1rem;
    margin: 1.5rem 0;
}

@media (min-width: 576px) {
    .books-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 colonnes sur tablette */
        gap: 1.25rem;
    }
}

@media (min-width: 768px) {
    .books-grid {
        grid-template-columns: repeat(4, 1fr); /* 4 colonnes sur petit desktop */
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .books-grid {
        grid-template-columns: repeat(5, 1fr); /* 5 colonnes sur grand desktop */
    }
}

@media (min-width: 1200px) {
    .books-grid {
        grid-template-columns: repeat(6, 1fr); /* 6 colonnes sur très grand desktop */
    }
}

/* Carte de livre - Mobile First */
.book-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Animation au scroll */
.book-card.fade-in {
    opacity: 0;
    transform: translateY(20px);
}

.book-card.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Effet tactile pour mobile */
.book-card:active {
    transform: scale(0.98);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Effet hover pour desktop */
@media (hover: hover) {
    .book-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    }

    .book-card:hover .book-cover img {
        transform: scale(1.05);
    }
}

.book-link {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    height: 100%;
    -webkit-tap-highlight-color: transparent; /* Supprime le flash bleu sur mobile */
}

.book-cover {
    position: relative;
    height: 0;
    padding-bottom: 150%; /* Ratio 2:3 pour les couvertures de livres */
    overflow: hidden;
    background-color: #f5f5f5; /* Couleur de fond pendant le chargement */
}

.book-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.book-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: var(--primary-color);
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
    .book-badge {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

.book-info {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

@media (min-width: 768px) {
    .book-info {
        padding: 1rem;
    }
}

.book-info h3 {
    margin: 0 0 0.3rem;
    font-size: 0.9rem;
    line-height: 1.3;
    font-weight: 600;
    /* Limiter à 2 lignes avec ellipsis */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@media (min-width: 768px) {
    .book-info h3 {
        font-size: 1rem;
        margin: 0 0 0.5rem;
    }
}

.book-author {
    color: #666;
    font-size: 0.8rem;
    margin: 0 0 0.3rem;
    /* Limiter à 1 ligne avec ellipsis */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (min-width: 768px) {
    .book-author {
        font-size: 0.9rem;
        margin: 0 0 0.5rem;
    }
}

.book-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
    margin-bottom: 0.3rem;
    font-size: 0.7rem;
}

@media (min-width: 768px) {
    .book-meta {
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        font-size: 0.8rem;
    }
}

.book-genre, .book-year {
    background-color: #f0f0f0;
    padding: 0.15rem 0.4rem;
    border-radius: 4px;
    color: #555;
}

@media (min-width: 768px) {
    .book-genre, .book-year {
        padding: 0.2rem 0.5rem;
    }
}

.book-rating {
    margin-top: auto;
    color: #f8b400;
    font-size: 0.8rem;
}

@media (min-width: 768px) {
    .book-rating {
        font-size: 0.9rem;
    }
}

.star {
    display: inline-block;
    margin-right: 1px;
}

.star.filled {
    color: #f8b400;
}

.no-rating {
    color: #999;
    font-size: 0.7rem;
    font-style: italic;
}

@media (min-width: 768px) {
    .no-rating {
        font-size: 0.8rem;
    }
}

.rating-source {
    color: #666;
    font-size: 0.6rem;
    font-style: italic;
    margin-left: 0.25rem;
    opacity: 0.8;
}

@media (min-width: 768px) {
    .rating-source {
        font-size: 0.7rem;
    }
}

/* Barre de recherche et filtres - Mobile First */
.search-filter-container {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
    .search-filter-container {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .search-form {
        gap: 1rem;
    }
}

.search-input-container {
    display: flex;
    width: 100%;
    position: relative;
}

.search-input-container input {
    flex-grow: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 0.9rem;
    -webkit-appearance: none; /* Supprime le style par défaut sur iOS */
}

@media (min-width: 768px) {
    .search-input-container input {
        font-size: 1rem;
    }
}

.search-input-container button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 1rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-weight: 600;
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (min-width: 768px) {
    .search-input-container button {
        padding: 0 1.5rem;
    }
}

/* Bouton pour afficher/masquer les filtres sur mobile */
.toggle-filters {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem;
    width: 100%;
    font-size: 0.9rem;
    font-weight: 600;
    color: #555;
    cursor: pointer;
    margin-top: 0.5rem;
}

.toggle-filters i {
    transition: transform 0.3s ease;
}

.toggle-filters.active i {
    transform: rotate(180deg);
}

@media (min-width: 768px) {
    .toggle-filters {
        display: none;
    }
}

.filters-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2 colonnes sur mobile */
    gap: 0.75rem;
    margin-top: 0.75rem;
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease;
}

.filters-container.show {
    max-height: 1000px; /* Valeur suffisamment grande */
}

@media (min-width: 768px) {
    .filters-container {
        grid-template-columns: repeat(3, 1fr); /* 3 colonnes sur tablette */
        gap: 1rem;
        margin-top: 1rem;
        max-height: none; /* Toujours visible sur desktop */
    }
}

@media (min-width: 1024px) {
    .filters-container {
        grid-template-columns: repeat(5, 1fr); /* 5 colonnes sur desktop */
    }
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

@media (min-width: 768px) {
    .filter-group {
        gap: 0.5rem;
    }
}

.filter-group label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #555;
}

@media (min-width: 768px) {
    .filter-group label {
        font-size: 0.9rem;
    }
}

.filter-group select {
    padding: 0.4rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 0.8rem;
    -webkit-appearance: none; /* Supprime le style par défaut sur iOS */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    padding-right: 1.5rem;
}

@media (min-width: 768px) {
    .filter-group select {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}

.filter-button {
    grid-column: 1 / -1;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

@media (min-width: 768px) {
    .filter-button {
        padding: 0.75rem;
        font-size: 1rem;
    }
}

@media (min-width: 1024px) {
    .filter-button {
        grid-column: auto;
        align-self: end;
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.pagination-link {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
}

.pagination-link:hover {
    background-color: #f0f0f0;
}

.pagination-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-link.prev, .pagination-link.next {
    background-color: #f9f9f9;
    font-weight: 600;
}

/* Résultats */
.results-info {
    color: #666;
    font-size: 0.9rem;
    margin: 1rem 0;
}

/* État vide */
.empty-state {
    text-align: center;
    padding: 3rem;
    background-color: #f9f9f9;
    border-radius: 8px;
    grid-column: 1 / -1;
}

.empty-state p {
    color: #666;
    margin-bottom: 1rem;
}

/* Page de détails du livre */
.book-details {
    margin: 2rem 0;
}

.book-details-header {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .book-details-header {
        flex-direction: row;
        align-items: flex-start;
    }
}

.book-cover-large {
    flex-shrink: 0;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .book-cover-large {
        width: 250px;
        margin: 0;
    }
}

.book-cover-large img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.book-info-large {
    flex-grow: 1;
}

.book-info-large h1 {
    margin: 0 0 0.5rem;
    font-size: 1.8rem;
    line-height: 1.3;
}

.book-author-large {
    font-size: 1.2rem;
    margin: 0 0 1rem;
    color: #555;
}

.book-meta-large {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.book-genre-badge, .book-year-badge {
    background-color: #f0f0f0;
    padding: 0.3rem 0.75rem;
    border-radius: 4px;
    color: #555;
    font-size: 0.9rem;
}

.book-isbn {
    color: #777;
    font-size: 0.9rem;
}

.book-rating-large {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.book-rating-large .stars {
    color: #f8b400;
    font-size: 1.5rem;
}

.rating-value {
    font-weight: 600;
    color: #333;
}

.book-actions, .book-actions-guest {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.action-info {
    color: #777;
    font-size: 0.9rem;
    font-style: italic;
}

.book-description {
    margin-bottom: 2rem;
}

.book-description h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: #333;
}

.no-description {
    color: #777;
    font-style: italic;
}

.book-reviews {
    margin-bottom: 2rem;
}

.book-reviews h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: #333;
}

.reviews-placeholder {
    background-color: #f9f9f9;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    color: #777;
}

.back-link {
    margin: 2rem 0;
}

.btn-text {
    background: none;
    border: none;
    color: var(--primary-color);
    padding: 0;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-text:hover {
    text-decoration: underline;
}
