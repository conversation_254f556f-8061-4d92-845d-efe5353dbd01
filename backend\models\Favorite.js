import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/db.js';

class Favorite extends Model {
  /**
   * Ajoute un livre aux favoris d'un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @param {number} bookId - ID du livre
   * @returns {Promise<Favorite>} - Le favori créé ou existant
   */
  static async addFavorite(userId, bookId) {
    try {
      // Utiliser findOrCreate pour éviter les erreurs de contrainte unique
      const [favorite, created] = await Favorite.findOrCreate({
        where: {
          user_id: userId,
          book_id: bookId
        },
        defaults: {
          user_id: userId,
          book_id: bookId
        }
      });

      console.log(`Favori ${created ? 'créé' : 'existait déjà'} pour userId: ${userId}, bookId: ${bookId}`);
      return favorite;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du livre aux favoris:', error);
      throw error;
    }
  }

  /**
   * Supprime un livre des favoris d'un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @param {number} bookId - ID du livre
   * @returns {Promise<boolean>} - true si supprimé, false sinon
   */
  static async removeFavorite(userId, bookId) {
    try {
      const result = await Favorite.destroy({
        where: {
          user_id: userId,
          book_id: bookId
        }
      });

      return result > 0;
    } catch (error) {
      console.error('Erreur lors de la suppression du livre des favoris:', error);
      throw error;
    }
  }

  /**
   * Vérifie si un livre est dans les favoris d'un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @param {number} bookId - ID du livre
   * @returns {Promise<boolean>} - true si le livre est dans les favoris, false sinon
   */
  static async isFavorite(userId, bookId) {
    try {
      const favorite = await Favorite.findOne({
        where: {
          user_id: userId,
          book_id: bookId
        }
      });

      return !!favorite;
    } catch (error) {
      console.error('Erreur lors de la vérification du favori:', error);
      throw error;
    }
  }

  /**
   * Récupère tous les favoris d'un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @param {Object} options - Options de filtrage et tri
   * @returns {Promise<Array>} - Les livres favoris
   */
  static async findByUserId(userId, options = {}) {
    try {
      const { limit = null, search = '', sort = 'title_asc', page = 1, pageSize = 20 } = options;

      // Calculer l'offset pour la pagination
      const offset = (page - 1) * pageSize;

      // Optimisation: Vérifier d'abord si l'utilisateur a des favoris
      // avant de faire des jointures complexes
      const favoriteCount = await Favorite.count({
        where: { user_id: userId }
      });

      if (favoriteCount === 0) {
        return [];
      }

      // Configuration de base de la requête
      const queryOptions = {
        where: { user_id: userId }
      };

      // Définir l'ordre de tri
      switch (sort) {
        case 'title_asc':
          queryOptions.order = [[{ model: sequelize.models.Book, as: 'book' }, 'title', 'ASC']];
          break;
        case 'title_desc':
          queryOptions.order = [[{ model: sequelize.models.Book, as: 'book' }, 'title', 'DESC']];
          break;
        case 'author_asc':
          queryOptions.order = [[{ model: sequelize.models.Book, as: 'book' }, 'author', 'ASC']];
          break;
        case 'author_desc':
          queryOptions.order = [[{ model: sequelize.models.Book, as: 'book' }, 'author', 'DESC']];
          break;
        case 'date_added_asc':
          queryOptions.order = [['created_at', 'ASC']];
          break;
        case 'date_added_desc':
          queryOptions.order = [['created_at', 'DESC']];
          break;
        default:
          queryOptions.order = [[{ model: sequelize.models.Book, as: 'book' }, 'title', 'ASC']];
      }

      // Optimisation: Si une recherche est spécifiée, utiliser une approche en deux étapes
      if (search) {
        // 1. Trouver d'abord les IDs des livres qui correspondent à la recherche
        const bookIds = await sequelize.models.Book.findAll({
          attributes: ['id'],
          where: {
            [sequelize.Op.or]: [
              { title: { [sequelize.Op.like]: `%${search}%` } },
              { author: { [sequelize.Op.like]: `%${search}%` } },
              { isbn: { [sequelize.Op.like]: `%${search}%` } }
            ]
          }
        }).then(books => books.map(book => book.id));

        // Si aucun livre ne correspond, retourner un tableau vide
        if (bookIds.length === 0) {
          return [];
        }

        // 2. Ajouter la condition pour filtrer par ces IDs
        queryOptions.where.book_id = { [sequelize.Op.in]: bookIds };
      }

      // Ajouter les options de pagination
      if (limit && Number.isInteger(limit) && limit > 0) {
        queryOptions.limit = limit;
      } else if (pageSize) {
        queryOptions.limit = pageSize;
        queryOptions.offset = offset;
      }

      // Inclure les relations nécessaires
      queryOptions.include = [
        {
          model: sequelize.models.Book,
          as: 'book',
          include: [
            {
              model: sequelize.models.Genre,
              as: 'genre',
              attributes: ['id', 'name']
            }
          ]
        }
      ];

      // Exécuter la requête optimisée
      return await Favorite.findAll(queryOptions);
    } catch (error) {
      console.error('Erreur lors de la récupération des favoris:', error);
      throw error;
    }
  }
}

// Définition du modèle
Favorite.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    book_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Books',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    sequelize,
    modelName: 'Favorite',
    tableName: 'FAVORITES',
    timestamps: true,
    updatedAt: false, // Pas besoin de updated_at pour cette table
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'book_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['book_id']
      }
    ]
  }
);

export default Favorite;
