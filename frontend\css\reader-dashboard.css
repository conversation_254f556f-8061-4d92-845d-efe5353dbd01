/**
 * Styles pour le dashboard lecteur
 * Inspiré du dashboard admin mais avec une identité propre
 */

/* ===== VARIABLES ===== */
:root {
    --reader-primary: #4a6fa5;
    --reader-secondary: #166088;
    --reader-accent: #f39c12;
    --reader-danger: #e74c3c;
    --reader-success: #2ecc71;
    --reader-warning: #f1c40f;
    --reader-info: #3498db;
    --reader-light: #f5f5f5;
    --reader-dark: #333;
    --reader-gray: #95a5a6;
    --reader-border: #ddd;
    --reader-sidebar-width: 250px;
    --reader-header-height: 60px;
    --reader-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --reader-transition: all 0.3s ease;
}

/* ===== LAYOUT GÉNÉRAL ===== */
.reader-body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.reader-container {
    display: flex;
    flex: 1;
    margin-top: var(--reader-header-height);
    position: relative;
}

.reader-header {
    background-color: var(--primary-color);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--reader-header-height);
    z-index: 1000;
    box-shadow: var(--reader-shadow);
}

.reader-header .brand {
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
}

.reader-header .nav-links a,
.reader-header .logout-button {
    color: white;
}

.reader-footer {
    background-color: var(--primary-dark);
    color: white;
    padding: 1rem;
    text-align: center;
    margin-left: var(--reader-sidebar-width);
    flex-shrink: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

/* ===== SIDEBAR ===== */
.reader-sidebar {
    width: var(--reader-sidebar-width);
    background-color: white;
    box-shadow: var(--reader-shadow);
    position: fixed;
    top: var(--reader-header-height);
    left: 0;
    bottom: 0;
    overflow-y: auto;
    z-index: 900;
    transition: var(--reader-transition);
}

.reader-sidebar.collapsed {
    width: 60px;
    overflow: hidden;
}

.reader-sidebar-toggle {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: var(--reader-gray);
    cursor: pointer;
    font-size: 1.2rem;
    z-index: 10;
    transition: var(--reader-transition);
}

.reader-sidebar-toggle:hover {
    color: var(--reader-primary);
}

.reader-user {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--reader-border);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--reader-transition);
}

.reader-sidebar.collapsed .reader-user {
    padding: 1.5rem 0.5rem;
    justify-content: center;
}

.reader-avatar {
    font-size: 2.5rem;
    color: var(--primary-color);
    flex-shrink: 0;
}

.reader-user-info {
    transition: var(--reader-transition);
    white-space: nowrap;
    overflow: hidden;
}

.reader-sidebar.collapsed .reader-user-info {
    opacity: 0;
    width: 0;
}

.reader-username {
    font-weight: bold;
    margin-bottom: 0.2rem;
    margin: 0;
}

.reader-role {
    color: var(--text-muted);
    margin: 0;
    font-size: 0.8rem;
}

.reader-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.reader-nav a {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: var(--text-color);
    text-decoration: none;
    border-left: 3px solid transparent;
    transition: var(--reader-transition);
    white-space: nowrap;
    overflow: hidden;
}

.reader-sidebar.collapsed .reader-nav a {
    padding: 1rem 0;
    justify-content: center;
}

.reader-nav a:hover {
    background-color: rgba(74, 111, 165, 0.1);
    border-left-color: var(--primary-color);
}

.reader-nav a.active {
    background-color: rgba(74, 111, 165, 0.2);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: bold;
}

.reader-nav i {
    margin-right: 0.8rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.reader-sidebar.collapsed .reader-nav i {
    margin-right: 0;
}

.nav-text {
    transition: var(--reader-transition);
}

.reader-sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    display: none;
}

/* ===== CONTENU PRINCIPAL ===== */
.reader-main {
    flex: 1;
    padding: 1.5rem;
    margin-left: var(--reader-sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - var(--reader-header-height) - 50px);
    transition: var(--reader-transition);
    overflow-x: hidden;
}

.reader-sidebar.collapsed ~ .reader-main,
.reader-sidebar.collapsed ~ .reader-footer {
    margin-left: 60px;
}

.reader-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--reader-border);
    flex-wrap: wrap;
}

.reader-page-header h1 {
    margin: 0 0 0.5rem;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.reader-page-header p {
    margin: 0;
    color: var(--text-muted);
}

/* ===== CARTES STATISTIQUES ===== */
.reader-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.reader-stat-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--reader-shadow);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reader-stat-icon {
    font-size: 2rem;
    color: var(--reader-primary);
    background-color: rgba(74, 111, 165, 0.1);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reader-stat-content {
    flex: 1;
}

.reader-stat-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    color: var(--reader-gray);
}

.reader-stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--reader-dark);
    margin: 0;
}

/* ===== ACTIONS RAPIDES ===== */
.reader-quick-actions {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--reader-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.reader-quick-actions h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: var(--reader-primary);
    font-size: 1.2rem;
}

.reader-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.reader-action-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    color: var(--reader-dark);
    text-decoration: none;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
}

.reader-action-card:hover {
    background-color: var(--reader-primary);
    color: white;
    transform: translateY(-5px);
}

.reader-action-card i {
    font-size: 2rem;
}

.reader-action-card:hover i {
    color: white;
}

/* ===== CARTES DE LISTES DE LECTURE ===== */
.reader-lists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.reader-list-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--reader-shadow);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    border: 1px solid var(--reader-border);
    height: 100%;
}

.reader-list-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.reader-list-card-header {
    padding: 1.25rem 1.25rem 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 1px solid var(--reader-border);
}

.reader-list-card-title {
    margin: 0;
    font-size: 1.25rem;
    color: var(--reader-primary);
}

.reader-list-card-title a {
    color: var(--reader-primary);
    text-decoration: none;
}

.reader-list-card-title a:hover {
    text-decoration: underline;
}

.reader-list-card-book-count {
    background-color: rgba(74, 111, 165, 0.1);
    color: var(--reader-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.reader-list-card-body {
    padding: 1rem 1.25rem;
    flex-grow: 1;
}

.reader-list-card-description {
    margin: 0;
    color: var(--reader-dark);
    font-size: 0.95rem;
    line-height: 1.5;
}

.reader-list-card-description.no-description {
    color: var(--reader-gray);
    font-style: italic;
}

.reader-list-card-actions {
    padding: 1rem 1.25rem;
    border-top: 1px solid var(--reader-border);
    background-color: rgba(0, 0, 0, 0.02);
    display: flex;
    gap: 0.75rem;
}

.reader-list-card-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
    padding: 0.35rem 0.75rem;
    font-size: 0.85rem;
}

.default-list-text {
    margin-left: auto;
    font-size: 0.85rem;
    color: var(--reader-gray);
    font-style: italic;
    align-self: center;
}

/* Styles pour la page de détails de liste */
.reader-list-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.reader-list-title-section {
    flex: 1;
    min-width: 250px;
}

.reader-list-title {
    margin: 0 0 0.5rem 0;
    color: var(--reader-primary);
    font-size: 1.75rem;
}

.reader-list-description {
    margin: 0;
    color: var(--reader-gray);
    font-size: 1rem;
    max-width: 600px;
}

.reader-list-actions {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

/* Responsive pour les listes */
@media (max-width: 768px) {
    .reader-lists-grid {
        grid-template-columns: 1fr;
    }
    
    .reader-list-header {
        flex-direction: column;
    }
    
    .reader-list-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* ===== MOBILE MENU ===== */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    margin-left: auto;
    margin-right: 1rem;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 992px) {
    .reader-sidebar {
        transform: translateX(-100%);
        z-index: 1000;
    }

    .reader-sidebar.active {
        transform: translateX(0);
    }

    .reader-main,
    .reader-footer {
        margin-left: 0 !important;
    }

    .reader-sidebar-toggle {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: var(--reader-header-height);
        left: 0;
        right: 0;
        background-color: var(--primary-color);
        flex-direction: column;
        padding: 1rem;
        z-index: 1001;
    }

    .nav-links.active {
        display: flex;
    }

    .nav-links li {
        margin: 0.5rem 0;
    }
}

@media (max-width: 768px) {
    .reader-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .reader-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .reader-main {
        padding: 1rem;
    }

    .reader-content-card {
        padding: 1rem;
    }

    .reader-stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .reader-stat-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .reader-stats-grid {
        grid-template-columns: 1fr;
    }

    .reader-main {
        padding: 0.75rem;
    }

    .reader-actions-grid {
        grid-template-columns: 1fr;
    }

    .reader-page-header {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }

    .reader-page-header h1 {
        font-size: 1.25rem;
    }

    .reader-action-card {
        padding: 1.25rem;
    }
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
.animate-in {
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.btn-hover {
    transform: translateY(-2px);
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.6s linear;
    opacity: 0;
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* ===== FORMULAIRES ===== */
.reader-form-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--reader-shadow);
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid var(--reader-border);
}

.reader-form .form-group {
    margin-bottom: 1.5rem;
}

.reader-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--reader-dark);
}

.reader-form input[type="text"],
.reader-form textarea,
.reader-form select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--reader-border);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.reader-form input[type="text"]:focus,
.reader-form textarea:focus,
.reader-form select:focus {
    border-color: var(--reader-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 111, 165, 0.2);
}

.reader-form small {
    display: block;
    margin-top: 0.25rem;
    color: var(--reader-gray);
    font-size: 0.85rem;
}

.reader-form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--reader-border);
}

.reader-form-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive pour les formulaires */
@media (max-width: 768px) {
    .reader-form-container {
        padding: 1.5rem;
    }
    
    .reader-form-actions {
        flex-direction: column;
    }
    
    .reader-form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}


