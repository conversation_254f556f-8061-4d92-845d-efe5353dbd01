import passport from 'passport';
import { User, ReadingList, Favorite, Book, Genre, Review } from '../models/index.js';

/**
 * Affiche la page d'inscription
 */
export async function showRegisterForm(req, res) {
    // Stocker l'URL de retour si elle est fournie dans les paramètres de requête
    if (req.query.returnTo) {
        req.session.returnTo = req.query.returnTo;
    }

    res.render('auth/register', {
        title: 'Inscription - Nookli',
        currentUser: req.user,
        error: req.flash('error'),
        formData: req.flash('formData')[0] || {}
    });
}

/**
 * Traite le formulaire d'inscription
 */
export async function register(req, res) {
    try {
        const { username, email, password, passwordConfirm } = req.body;

        // Stocker les données du formulaire pour les réafficher en cas d'erreur
        req.flash('formData', { username, email });

        // Validation de base
        if (!username || !email || !password) {
            req.flash('error', 'Tous les champs sont obligatoires');
            return res.redirect('/register');
        }

        // Vérifier que les mots de passe correspondent
        if (password !== passwordConfirm) {
            req.flash('error', 'Les mots de passe ne correspondent pas');
            return res.redirect('/register');
        }

        // Vérifier si l'utilisateur existe déjà
        const existingUserByUsername = await User.findOne({ where: { username } });
        if (existingUserByUsername) {
            req.flash('error', 'Ce nom d\'utilisateur est déjà utilisé');
            return res.redirect('/register');
        }

        const existingUserByEmail = await User.findOne({ where: { email } });
        if (existingUserByEmail) {
            req.flash('error', 'Cette adresse email est déjà utilisée');
            return res.redirect('/register');
        }

        // Créer l'utilisateur
        const newUser = await User.create({
            username,
            email,
            password,
            role: 'reader' // Par défaut, tous les nouveaux utilisateurs sont des lecteurs
        });

        // Créer les listes de lecture par défaut pour le nouvel utilisateur
        try {
            await ReadingList.createDefaultLists(newUser.id);
            console.log(`Listes de lecture par défaut créées pour l'utilisateur ${newUser.id}`);
        } catch (listError) {
            console.error('Erreur lors de la création des listes par défaut:', listError);
            // Ne pas bloquer l'inscription si la création des listes échoue
        }

        req.flash('success', 'Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.');

        // Rediriger vers la page de connexion en préservant l'URL de retour
        const returnTo = req.session.returnTo;
        if (returnTo) {
            res.redirect(`/login?returnTo=${encodeURIComponent(returnTo)}`);
        } else {
            res.redirect('/login');
        }
    } catch (error) {
        console.error('Erreur lors de l\'inscription:', error);
        req.flash('error', 'Une erreur est survenue lors de l\'inscription. Veuillez réessayer.');
        res.redirect('/register');
    }
}

/**
 * Affiche la page de connexion
 */
export async function showLoginForm(req, res) {
    // Stocker l'URL de retour si elle est fournie dans les paramètres de requête
    if (req.query.returnTo) {
        req.session.returnTo = req.query.returnTo;
    }

    res.render('auth/login', {
        title: 'Connexion - Nookli',
        currentUser: req.user,
        error: req.flash('error'),
        success: req.flash('success')
    });
}

/**
 * Traite le formulaire de connexion
 */
export function login(req, res, next) {
    passport.authenticate('local', (err, user, info) => {
        if (err) {
            return next(err);
        }

        if (!user) {
            req.flash('error', info.message || 'Identifiants incorrects');
            return res.redirect('/login');
        }

        req.logIn(user, (err) => {
            if (err) {
                return next(err);
            }

            // Rediriger vers la page appropriée selon le rôle de l'utilisateur
            let redirectUrl;

            // Si l'utilisateur est un admin, le rediriger vers l'interface d'administration
            if (user.role === 'admin') {
                redirectUrl = '/admin';
            } else {
                // Sinon, rediriger vers la page d'origine ou le tableau de bord
                redirectUrl = req.session.returnTo || '/dashboard';
                delete req.session.returnTo;
            }

            req.flash('success', 'Vous êtes maintenant connecté');
            return res.redirect(redirectUrl);
        });
    })(req, res, next);
}

/**
 * Déconnecte l'utilisateur
 */
export function logout(req, res, next) {
    req.logout((err) => {
        if (err) {
            return next(err);
        }
        req.flash('success', 'Vous avez été déconnecté avec succès');
        res.redirect('/');
    });
}

/**
 * Affiche le tableau de bord de l'utilisateur
 */
export async function showDashboard(req, res) {
    try {
        const userId = req.user.id;

        // Vérifier si l'utilisateur a des listes de lecture par défaut
        const listCount = await ReadingList.count({
            where: { user_id: userId }
        });

        // Si l'utilisateur n'a pas de listes, créer les listes par défaut
        if (listCount === 0) {
            console.log(`Création des listes par défaut pour l'utilisateur ${userId}`);
            await ReadingList.createDefaultLists(userId);
        }

        // Récupérer les statistiques de lecture
        const [toReadCount, inProgressCount, readCount, reviewCount] = await Promise.all([
            // Nombre de livres dans la liste "À lire"
            ReadingList.findOne({
                where: { user_id: userId, list_name: 'À lire' },
                include: [{
                    model: Book,
                    as: 'books'
                }]
            }).then(list => list ? list.books.length : 0),

            // Nombre de livres dans la liste "En cours"
            ReadingList.findOne({
                where: { user_id: userId, list_name: 'En cours' },
                include: [{
                    model: Book,
                    as: 'books'
                }]
            }).then(list => list ? list.books.length : 0),

            // Nombre de livres dans la liste "Lu"
            ReadingList.findOne({
                where: { user_id: userId, list_name: 'Lu' },
                include: [{
                    model: Book,
                    as: 'books'
                }]
            }).then(list => list ? list.books.length : 0),

            // Nombre d'avis publiés
            Review.count({ where: { user_id: userId } })
        ]);

        // Récupérer les livres en cours de lecture (limité à 5)
        const inProgressList = await ReadingList.findOne({
            where: { user_id: userId, list_name: 'En cours' },
            include: [{
                model: Book,
                as: 'books',
                include: [{
                    model: Genre,
                    as: 'genre'
                }]
            }]
        });
        const inProgressBooks = inProgressList ? inProgressList.books.slice(0, 5) : [];

        // Récupérer les livres à lire (limité à 5)
        const toReadList = await ReadingList.findOne({
            where: { user_id: userId, list_name: 'À lire' },
            include: [{
                model: Book,
                as: 'books',
                include: [{
                    model: Genre,
                    as: 'genre'
                }]
            }]
        });
        const toReadBooks = toReadList ? toReadList.books.slice(0, 5) : [];

        // Récupérer les livres lus (limité à 5)
        const readList = await ReadingList.findOne({
            where: { user_id: userId, list_name: 'Lu' },
            include: [{
                model: Book,
                as: 'books',
                include: [{
                    model: Genre,
                    as: 'genre'
                }]
            }]
        });
        const readBooks = readList ? readList.books.slice(0, 5) : [];

        // Récupérer les favoris (limité à 5)
        const favorites = await Favorite.findByUserId(userId, 5);

        // Récupérer les derniers avis de l'utilisateur (limité à 3)
        const recentReviews = await Review.findAll({
            where: { user_id: userId },
            limit: 3,
            order: [['created_at', 'DESC']],
            include: [{
                model: Book,
                as: 'book',
                include: [{
                    model: Genre,
                    as: 'genre'
                }]
            }]
        });

        res.render('auth/dashboard', {
            title: 'Mon espace - Nookli',
            currentUser: req.user,
            stats: {
                toReadCount,
                inProgressCount,
                readCount,
                reviewCount
            },
            inProgressBooks,
            toReadBooks,
            readBooks,
            favorites: favorites.map(fav => fav.book), // Extraire juste les livres
            recentReviews,
            path: '/dashboard',
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors du chargement du tableau de bord:', error);
        req.flash('error', 'Une erreur est survenue lors du chargement de votre espace personnel.');
        res.render('auth/dashboard', {
            title: 'Mon espace - Nookli',
            currentUser: req.user,
            stats: {
                toReadCount: 0,
                inProgressCount: 0,
                readCount: 0,
                reviewCount: 0
            },
            inProgressBooks: [],
            toReadBooks: [],
            readBooks: [],
            favorites: [],
            recentReviews: [],
            path: '/dashboard',
            success: req.flash('success'),
            error: req.flash('error')
        });
    }
}

/**
 * Affiche la page des listes de lecture
 */
export async function showReadingLists(req, res) {
    try {
        const userId = req.user.id;

        // Vérifier si l'utilisateur a des listes de lecture par défaut
        const listCount = await ReadingList.count({
            where: { user_id: userId }
        });

        // Si l'utilisateur n'a pas de listes, créer les listes par défaut
        if (listCount === 0) {
            console.log(`Création des listes par défaut pour l'utilisateur ${userId}`);
            await ReadingList.createDefaultLists(userId);
        }

        // Récupérer les listes de lecture avec les livres
        const readingLists = await ReadingList.findByUserIdWithBooks(userId);

        res.render('auth/reading-lists', {
            title: 'Mes listes de lecture - Nookli',
            currentUser: req.user,
            readingLists,
            path: '/dashboard/reading-lists',
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors du chargement des listes de lecture:', error);
        req.flash('error', 'Une erreur est survenue lors du chargement de vos listes de lecture.');
        res.render('auth/reading-lists', {
            title: 'Mes listes de lecture - Nookli',
            currentUser: req.user,
            readingLists: [],
            path: '/dashboard/reading-lists',
            success: req.flash('success'),
            error: req.flash('error')
        });
    }
}

/**
 * Affiche la page des favoris
 */
export async function showFavorites(req, res) {
    try {
        const userId = req.user.id;

        // Récupérer les favoris
        const favorites = await Favorite.findByUserId(userId);

        res.render('auth/favorites', {
            title: 'Mes favoris - Nookli',
            currentUser: req.user,
            favorites: favorites.map(fav => fav.book), // Extraire juste les livres
            path: '/dashboard/favorites',
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors du chargement des favoris:', error);
        req.flash('error', 'Une erreur est survenue lors du chargement de vos favoris.');
        res.render('auth/favorites', {
            title: 'Mes favoris - Nookli',
            currentUser: req.user,
            favorites: [],
            path: '/dashboard/favorites',
            success: req.flash('success'),
            error: req.flash('error')
        });
    }
}

/**
 * Affiche la page des avis
 */
export async function showReviews(req, res) {
    try {
        const userId = req.user.id;

        // Récupérer tous les avis de l'utilisateur
        const recentReviews = await Review.findAll({
            where: { user_id: userId },
            order: [['created_at', 'DESC']],
            include: [{
                model: Book,
                as: 'book',
                include: [{
                    model: Genre,
                    as: 'genre'
                }]
            }]
        });

        res.render('auth/reviews', {
            title: 'Mes avis - Nookli',
            currentUser: req.user,
            recentReviews,
            path: '/dashboard/reviews',
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors du chargement des avis:', error);
        req.flash('error', 'Une erreur est survenue lors du chargement de vos avis.');
        res.render('auth/reviews', {
            title: 'Mes avis - Nookli',
            currentUser: req.user,
            recentReviews: [],
            path: '/dashboard/reviews',
            success: req.flash('success'),
            error: req.flash('error')
        });
    }
}

/**
 * Affiche les détails d'une liste de lecture
 */
export async function showReadingListDetails(req, res) {
    try {
        const userId = req.user.id;
        const listId = req.params.id;

        // Vérifier si l'utilisateur a des listes de lecture par défaut
        const listCount = await ReadingList.count({
            where: { user_id: userId }
        });

        // Si l'utilisateur n'a pas de listes, créer les listes par défaut
        if (listCount === 0) {
            console.log(`Création des listes par défaut pour l'utilisateur ${userId}`);
            await ReadingList.createDefaultLists(userId);
            // Rediriger vers la page des listes car l'ID demandé n'existera pas encore
            req.flash('info', 'Vos listes de lecture par défaut ont été créées.');
            return res.redirect('/dashboard/reading-lists');
        }

        // Récupérer la liste de lecture avec ses livres
        const readingList = await ReadingList.findOne({
            where: { id: listId, user_id: userId },
            include: [{
                model: Book,
                as: 'books',
                include: [{
                    model: Genre,
                    as: 'genre'
                }]
            }]
        });

        if (!readingList) {
            req.flash('error', 'Liste de lecture non trouvée');
            return res.redirect('/dashboard/reading-lists');
        }

        res.render('auth/reading-list-details', {
            title: `${readingList.list_name} - Mes listes de lecture - Nookli`,
            currentUser: req.user,
            readingList,
            path: `/dashboard/reading-lists/${listId}`,
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors du chargement des détails de la liste:', error);
        req.flash('error', 'Une erreur est survenue lors du chargement des détails de la liste.');
        res.redirect('/dashboard/reading-lists');
    }
}

/**
 * Affiche la page de profil
 */
export async function showProfile(req, res) {
    try {
        const userId = req.user.id;

        // Vérifier si l'utilisateur a des listes de lecture par défaut
        const listCount = await ReadingList.count({
            where: { user_id: userId }
        });

        // Si l'utilisateur n'a pas de listes, créer les listes par défaut
        if (listCount === 0) {
            console.log(`Création des listes par défaut pour l'utilisateur ${userId}`);
            await ReadingList.createDefaultLists(userId);
        }

        // Récupérer les statistiques de lecture
        const [toReadCount, inProgressCount, readCount, reviewCount] = await Promise.all([
            // Nombre de livres dans la liste "À lire"
            ReadingList.findOne({
                where: { user_id: userId, list_name: 'À lire' },
                include: [{
                    model: Book,
                    as: 'books'
                }]
            }).then(list => list ? list.books.length : 0),

            // Nombre de livres dans la liste "En cours"
            ReadingList.findOne({
                where: { user_id: userId, list_name: 'En cours' },
                include: [{
                    model: Book,
                    as: 'books'
                }]
            }).then(list => list ? list.books.length : 0),

            // Nombre de livres dans la liste "Lu"
            ReadingList.findOne({
                where: { user_id: userId, list_name: 'Lu' },
                include: [{
                    model: Book,
                    as: 'books'
                }]
            }).then(list => list ? list.books.length : 0),

            // Nombre d'avis publiés
            Review.count({ where: { user_id: userId } })
        ]);

        res.render('auth/profile', {
            title: 'Mon profil - Nookli',
            currentUser: req.user,
            stats: {
                toReadCount,
                inProgressCount,
                readCount,
                reviewCount
            },
            path: '/profile',
            success: req.flash('success'),
            error: req.flash('error')
        });
    } catch (error) {
        console.error('Erreur lors du chargement du profil:', error);
        req.flash('error', 'Une erreur est survenue lors du chargement de votre profil.');
        res.render('auth/profile', {
            title: 'Mon profil - Nookli',
            currentUser: req.user,
            stats: {
                toReadCount: 0,
                inProgressCount: 0,
                readCount: 0,
                reviewCount: 0
            },
            path: '/profile',
            success: req.flash('success'),
            error: req.flash('error')
        });
    }
}

// Exporter toutes les fonctions
export default {
    showRegisterForm,
    register,
    showLoginForm,
    login,
    logout,
    showDashboard,
    showReadingLists,
    showReadingListDetails,
    showFavorites,
    showReviews,
    showProfile
};