/**
 * Utilitaire pour gérer les fichiers (téléchargement, stockage, etc.)
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import https from 'https';
import http from 'http';

// --- Calculer __dirname ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Chemins des dossiers de stockage
const UPLOAD_DIR = path.resolve(__dirname, '../../public/uploads');
const COVERS_DIR = path.resolve(UPLOAD_DIR, 'covers');
const EBOOKS_DIR = path.resolve(UPLOAD_DIR, 'ebooks');

/**
 * Crée les dossiers de stockage s'ils n'existent pas
 */
function ensureDirectoriesExist() {
    [UPLOAD_DIR, COVERS_DIR, EBOOKS_DIR].forEach(dir => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`Dossier créé: ${dir}`);
        }
    });
}

/**
 * Génère un nom de fichier sécurisé à partir d'un titre de livre
 * @param {string} title - Titre du livre
 * @param {string} extension - Extension du fichier (sans le point)
 * @returns {string} - Nom de fichier sécurisé
 */
function generateSafeFilename(title, extension) {
    // Remplacer les caractères spéciaux et les espaces par des tirets
    let safeTitle = title
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');

    // Ajouter un timestamp pour éviter les collisions
    const timestamp = Date.now();

    return `${safeTitle}-${timestamp}.${extension}`;
}

/**
 * Télécharge un fichier depuis une URL et le sauve localement
 * @param {string} url - URL du fichier à télécharger
 * @param {string} destPath - Chemin de destination
 * @returns {Promise<string>} - Chemin du fichier téléchargé
 */
function downloadFile(url, destPath) {
    return new Promise((resolve, reject) => {
        // Vérifier si l'URL est valide
        if (!url || !url.startsWith('http')) {
            return reject(new Error('URL invalide'));
        }

        // Créer le dossier de destination s'il n'existe pas
        const destDir = path.dirname(destPath);
        if (!fs.existsSync(destDir)) {
            fs.mkdirSync(destDir, { recursive: true });
        }

        // Choisir le module http ou https selon l'URL
        const httpClient = url.startsWith('https') ? https : http;

        // Télécharger le fichier
        const fileStream = fs.createWriteStream(destPath);

        httpClient.get(url, (response) => {
            // Vérifier le code de statut
            if (response.statusCode !== 200) {
                fileStream.close();
                fs.unlinkSync(destPath); // Supprimer le fichier partiel
                return reject(new Error(`Erreur HTTP: ${response.statusCode}`));
            }

            // Pipe la réponse vers le fichier
            response.pipe(fileStream);

            // Gérer les événements
            fileStream.on('finish', () => {
                fileStream.close();
                resolve(destPath);
            });
        }).on('error', (err) => {
            fileStream.close();
            fs.unlinkSync(destPath); // Supprimer le fichier partiel
            reject(err);
        });
    });
}

/**
 * Télécharge une image de couverture depuis une URL
 * @param {string} url - URL de l'image
 * @param {string} slug - Slug du livre (pour générer le nom de fichier)
 * @param {string} oldFilePath - Chemin de l'ancien fichier à supprimer (optionnel)
 * @returns {Promise<Object>} - Objet contenant le nom du fichier et le chemin
 */
async function downloadCoverImage(url, slug, oldFilePath = null) {
    try {
        ensureDirectoriesExist();

        // Déterminer l'extension du fichier à partir de l'URL
        const extension = path.extname(new URL(url).pathname).slice(1) || 'jpg';

        // Générer un nom de fichier sécurisé basé sur le slug
        const filename = `${slug}.${extension}`;

        // Chemin complet du fichier
        const filePath = path.join(COVERS_DIR, filename);

        // Supprimer l'ancien fichier s'il existe et est différent du nouveau
        if (oldFilePath && oldFilePath.startsWith('/uploads/') && oldFilePath !== `/uploads/covers/${filename}`) {
            const oldAbsolutePath = path.join(path.dirname(COVERS_DIR), oldFilePath.substring(1));
            await deleteFile(oldAbsolutePath);
        }

        // Télécharger le fichier
        await downloadFile(url, filePath);

        // Retourner les informations du fichier
        return {
            filename,
            path: filePath,
            relativePath: `/uploads/covers/${filename}`
        };
    } catch (error) {
        console.error(`Erreur lors du téléchargement de l'image de couverture:`, error);
        throw error;
    }
}

/**
 * Télécharge un ebook depuis une URL
 * @param {string} url - URL de l'ebook
 * @param {string} slug - Slug du livre (pour générer le nom de fichier)
 * @param {string} oldFilePath - Chemin de l'ancien fichier à supprimer (optionnel)
 * @returns {Promise<Object>} - Objet contenant le nom du fichier et le chemin
 */
async function downloadEbook(url, slug, oldFilePath = null) {
    try {
        ensureDirectoriesExist();

        // Déterminer l'extension du fichier à partir de l'URL
        const extension = path.extname(new URL(url).pathname).slice(1) || 'pdf';

        // Générer un nom de fichier sécurisé basé sur le slug
        const filename = `${slug}.${extension}`;

        // Chemin complet du fichier
        const filePath = path.join(EBOOKS_DIR, filename);

        // Supprimer l'ancien fichier s'il existe et est différent du nouveau
        if (oldFilePath && oldFilePath.startsWith('/uploads/') && oldFilePath !== `/uploads/ebooks/${filename}`) {
            const oldAbsolutePath = path.join(path.dirname(EBOOKS_DIR), oldFilePath.substring(1));
            await deleteFile(oldAbsolutePath);
        }

        // Télécharger le fichier
        await downloadFile(url, filePath);

        // Retourner les informations du fichier
        return {
            filename,
            path: filePath,
            relativePath: `/uploads/ebooks/${filename}`
        };
    } catch (error) {
        console.error(`Erreur lors du téléchargement de l'ebook:`, error);
        throw error;
    }
}

/**
 * Supprime un fichier
 * @param {string} filePath - Chemin du fichier à supprimer
 * @returns {Promise<boolean>} - true si supprimé, false sinon
 */
async function deleteFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`Fichier supprimé: ${filePath}`);
            return true;
        }
        console.log(`Fichier non trouvé: ${filePath}`);
        return false;
    } catch (error) {
        console.error(`Erreur lors de la suppression du fichier:`, error);
        return false;
    }
}

/**
 * Sauvegarde une image de couverture uploadée
 * @param {Object} file - Objet fichier uploadé
 * @param {string} slug - Slug du livre (pour générer le nom de fichier)
 * @param {string} oldFilePath - Chemin de l'ancien fichier à supprimer (optionnel)
 * @returns {Promise<Object>} - Objet contenant le nom du fichier et le chemin
 */
async function saveCoverImage(file, slug, oldFilePath = null) {
    try {
        ensureDirectoriesExist();

        // Déterminer l'extension du fichier à partir du mimetype
        const extension = file.mimetype.split('/')[1] || 'jpg';

        // Générer un nom de fichier sécurisé basé sur le slug
        const filename = `${slug}.${extension}`;

        // Chemin complet du fichier
        const filePath = path.join(COVERS_DIR, filename);

        // Supprimer l'ancien fichier s'il existe et est différent du nouveau
        if (oldFilePath && oldFilePath.startsWith('/uploads/') && oldFilePath !== `/uploads/covers/${filename}`) {
            const oldAbsolutePath = path.join(path.dirname(COVERS_DIR), oldFilePath.substring(1));
            await deleteFile(oldAbsolutePath);
        }

        // Déplacer le fichier uploadé vers le dossier de destination
        await file.mv(filePath);

        // Retourner les informations du fichier
        return {
            filename,
            path: filePath,
            relativePath: `/uploads/covers/${filename}`
        };
    } catch (error) {
        console.error(`Erreur lors de la sauvegarde de l'image de couverture:`, error);
        throw error;
    }
}

/**
 * Sauvegarde un fichier ebook uploadé
 * @param {Object} file - Objet fichier uploadé
 * @param {string} slug - Slug du livre (pour générer le nom de fichier)
 * @param {string} oldFilePath - Chemin de l'ancien fichier à supprimer (optionnel)
 * @returns {Promise<Object>} - Objet contenant le nom du fichier et le chemin
 */
async function saveEbookFile(file, slug, oldFilePath = null) {
    try {
        ensureDirectoriesExist();

        // Déterminer l'extension du fichier à partir du mimetype
        const extension = file.mimetype === 'application/pdf' ? 'pdf' : 'epub';

        // Générer un nom de fichier sécurisé basé sur le slug
        const filename = `${slug}.${extension}`;

        // Chemin complet du fichier
        const filePath = path.join(EBOOKS_DIR, filename);

        // Supprimer l'ancien fichier s'il existe et est différent du nouveau
        if (oldFilePath && oldFilePath.startsWith('/uploads/') && oldFilePath !== `/uploads/ebooks/${filename}`) {
            const oldAbsolutePath = path.join(path.dirname(EBOOKS_DIR), oldFilePath.substring(1));
            await deleteFile(oldAbsolutePath);
        }

        // Déplacer le fichier uploadé vers le dossier de destination
        await file.mv(filePath);

        // Retourner les informations du fichier
        return {
            filename,
            path: filePath,
            relativePath: `/uploads/ebooks/${filename}`
        };
    } catch (error) {
        console.error(`Erreur lors de la sauvegarde du fichier ebook:`, error);
        throw error;
    }
}

export default {
    ensureDirectoriesExist,
    generateSafeFilename,
    downloadFile,
    downloadCoverImage,
    downloadEbook,
    saveCoverImage,
    saveEbookFile,
    deleteFile,
    COVERS_DIR,
    EBOOKS_DIR
};
