import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/db.js';

class BookListEntry extends Model {
  /**
   * Ajoute un livre à une liste de lecture
   * @param {number} listId - ID de la liste
   * @param {number} bookId - ID du livre
   * @returns {Promise<BookListEntry>} - L'entrée créée
   */
  static async addBookToList(listId, bookId) {
    try {
      // Vérifier si l'entrée existe déjà
      const existingEntry = await BookListEntry.findOne({
        where: {
          list_id: listId,
          book_id: bookId
        }
      });

      if (existingEntry) {
        return existingEntry;
      }

      // Créer une nouvelle entrée
      return await BookListEntry.create({
        list_id: listId,
        book_id: bookId
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout du livre à la liste:', error);
      throw error;
    }
  }

  /**
   * Supprime un livre d'une liste de lecture
   * @param {number} listId - ID de la liste
   * @param {number} bookId - ID du livre
   * @returns {Promise<boolean>} - true si supprimé, false sinon
   */
  static async removeBookFromList(listId, bookId) {
    try {
      const result = await BookListEntry.destroy({
        where: {
          list_id: listId,
          book_id: bookId
        }
      });

      return result > 0;
    } catch (error) {
      console.error('Erreur lors de la suppression du livre de la liste:', error);
      throw error;
    }
  }

  /**
   * Vérifie si un livre est dans une liste
   * @param {number} listId - ID de la liste
   * @param {number} bookId - ID du livre
   * @returns {Promise<boolean>} - true si le livre est dans la liste, false sinon
   */
  static async isBookInList(listId, bookId) {
    try {
      const entry = await BookListEntry.findOne({
        where: {
          list_id: listId,
          book_id: bookId
        }
      });

      return !!entry;
    } catch (error) {
      console.error('Erreur lors de la vérification du livre dans la liste:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les listes contenant un livre pour un utilisateur
   * @param {number} userId - ID de l'utilisateur
   * @param {number} bookId - ID du livre
   * @returns {Promise<Array>} - Les listes contenant le livre
   */
  static async findListsContainingBook(userId, bookId) {
    try {
      const lists = await sequelize.models.ReadingList.findAll({
        include: [
          {
            model: BookListEntry,
            as: 'entries',
            where: { book_id: bookId },
            required: true
          }
        ],
        where: { user_id: userId }
      });

      return lists;
    } catch (error) {
      console.error('Erreur lors de la récupération des listes contenant le livre:', error);
      throw error;
    }
  }
}

// Définition du modèle
BookListEntry.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    list_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'ReadingLists',
        key: 'id'
      }
    },
    book_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Books',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    sequelize,
    modelName: 'BookListEntry',
    tableName: 'BOOK_LIST_ENTRIES',
    timestamps: true,
    updatedAt: false, // Pas besoin de updated_at pour cette table
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['list_id', 'book_id']
      },
      {
        fields: ['list_id']
      },
      {
        fields: ['book_id']
      }
    ]
  }
);

export default BookListEntry;
