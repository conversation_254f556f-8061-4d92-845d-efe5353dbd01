<%- include('layout', {
    path: '/admin/users',
    success: typeof success !== 'undefined' ? success : '',
    error: typeof error !== 'undefined' ? error : '',
    info: typeof info !== 'undefined' ? info : '',
    body: `
<div class="admin-page-header">
    <h1>${formTitle}</h1>
    <a href="/admin/users" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Retour à la liste
    </a>
</div>

<div class="admin-form-container">
    <form action="${formAction}" method="POST" class="admin-form">
        <div class="admin-form-group">
            <label for="username">Nom d'utilisateur *</label>
            <input type="text" id="username" name="username" value="${user.username || ''}" required>
        </div>

        <div class="admin-form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" value="${user.email || ''}" required>
        </div>

        <div class="admin-form-group">
            <label for="role">Rôle *</label>
            <select id="role" name="role" required>
                <option value="reader" ${user.role === 'reader' ? 'selected' : ''}>Lecteur</option>
                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Administrateur</option>
            </select>
        </div>

        <div class="admin-form-group">
            <label for="password">Mot de passe ${user.id ? '(laisser vide pour ne pas modifier)' : '*'}</label>
            <input type="password" id="password" name="password" ${!user.id ? 'required' : ''}>
            <small>Minimum 8 caractères</small>
        </div>

        <div class="admin-form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Enregistrer
            </button>
            <a href="/admin/users" class="btn btn-secondary">Annuler</a>
        </div>
    </form>
</div>
` }) %>
