-- Suppression des tables existantes pour repartir de zéro (prudence en dev!)
DROP TABLE IF EXISTS FAVORITES;
DROP TABLE IF EXISTS BOOK_LIST_ENTRIES;
DROP TABLE IF EXISTS READING_LISTS;
DROP TABLE IF EXISTS REVIEWS;
DROP TABLE IF EXISTS BOOKS;
DROP TABLE IF EXISTS GENRES;
DROP TABLE IF EXISTS USERS;

-- =============================================
-- Table: USERS
-- Description: Stocke les informations des utilisateurs enregistrés.
-- =============================================
CREATE TABLE USERS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'utilisateur
    username TEXT UNIQUE NOT NULL,              -- Nom d'utilisateur choisi (unique)
    email TEXT UNIQUE NOT NULL,                 -- Adresse email (unique, pour connexion/contact)
    password_hash TEXT NOT NULL,                -- Mot de passe haché (bcrypt)
    role TEXT NOT NULL DEFAULT 'reader'         -- Rôle ('reader' ou 'admin')
        CHECK(role IN ('reader', 'admin')),
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création du compte
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Date de dernière mise à jour du compte
);

-- =============================================
-- Table: GENRES
-- Description: Stocke les genres littéraires possibles.
-- =============================================
CREATE TABLE GENRES (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique du genre
    name TEXT UNIQUE NOT NULL,                  -- Nom du genre (ex: 'Science-Fiction')
    description TEXT,                           -- Description du genre (optionnelle)
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création du genre
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Date de dernière mise à jour du genre
);

-- =============================================
-- Table: BOOKS
-- Description: Stocke les informations sur chaque livre du catalogue.
-- =============================================
CREATE TABLE BOOKS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique du livre
    title TEXT NOT NULL,                        -- Titre du livre
    author TEXT NOT NULL,                       -- Auteur(s) du livre
    isbn TEXT UNIQUE,                           -- ISBN (optionnel mais unique si fourni)
    summary TEXT,                               -- Résumé court du livre
    description TEXT,                           -- Description détaillée du livre
    slug TEXT,                                  -- Version formatée du titre-auteur-langue-année pour URLs et fichiers
    cover_image_url TEXT,                       -- URL ou chemin local vers l'image de couverture
    publication_year INTEGER,                   -- Année de publication
    pdf_url TEXT NULL,                          -- URL ou chemin local vers le fichier PDF
    genre_id INTEGER,                           -- Clé étrangère vers GENRES.id
    created_by INTEGER,                         -- Clé étrangère vers USERS.id (l'admin qui a ajouté)
    average_rating REAL DEFAULT 0.0,            -- Note moyenne calculée à partir des REVIEWS
    initial_rating REAL,                        -- Note initiale du livre (si connu, sinon NULL)
    language TEXT DEFAULT "fr",                 -- Langue du livre (fr, en, it, etc.)
    status TEXT DEFAULT "draft"                 -- Statut du livre (draft, published)
        CHECK(status IN ("draft", "published")),
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date d'ajout du livre à la base
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de dernière mise à jour du livre
    FOREIGN KEY (genre_id) REFERENCES GENRES(id)
        ON DELETE SET NULL,                     -- Si le genre est supprimé, le champ devient NULL
    FOREIGN KEY (created_by) REFERENCES USERS(id)
        ON DELETE SET NULL                      -- Si l'admin est supprimé, le champ devient NULL
);

-- =============================================
-- Table: REVIEWS
-- Description: Stocke les avis (note + commentaire) des utilisateurs sur les livres.
-- Contrainte: Un utilisateur ne peut laisser qu'un seul avis par livre.
-- =============================================
CREATE TABLE REVIEWS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'avis
    book_id INTEGER NOT NULL,                   -- Clé étrangère vers BOOKS.id
    user_id INTEGER NOT NULL,                   -- Clé étrangère vers USERS.id
    rating INTEGER NOT NULL                     -- Note donnée (1 à 5)
        CHECK(rating >= 1 AND rating <= 5),
    review_text TEXT,                           -- Commentaire textuel (peut être vide/NULL)
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création de l'avis
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de dernière modification de l'avis
    FOREIGN KEY (book_id) REFERENCES BOOKS(id)
        ON DELETE CASCADE,                      -- Si le livre est supprimé, ses avis le sont aussi
    FOREIGN KEY (user_id) REFERENCES USERS(id)
        ON DELETE CASCADE,                      -- Si l'utilisateur est supprimé, ses avis le sont aussi
    UNIQUE(user_id, book_id)                    -- Contrainte: un seul avis par user/livre
);

-- =============================================
-- Table: READING_LISTS
-- Description: Stocke les listes de lecture créées par les utilisateurs.
-- Inclut les listes par défaut ('À lire', 'En cours', 'Lu').
-- =============================================
CREATE TABLE READING_LISTS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de la liste
    user_id INTEGER NOT NULL,                   -- Clé étrangère vers USERS.id (propriétaire)
    list_name TEXT NOT NULL,                    -- Nom de la liste (unique par utilisateur)
    is_default BOOLEAN NOT NULL DEFAULT FALSE,  -- TRUE pour les 3 listes créées à l'inscription
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création de la liste
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de dernière modification (ex: renommage)
    FOREIGN KEY (user_id) REFERENCES USERS(id)
        ON DELETE CASCADE,                      -- Si l'utilisateur est supprimé, ses listes aussi
    UNIQUE(user_id, list_name)                  -- Contrainte: nom de liste unique par utilisateur
);

-- =============================================
-- Table: BOOK_LIST_ENTRIES
-- Description: Table de jointure pour associer les livres aux listes de lecture.
-- =============================================
CREATE TABLE BOOK_LIST_ENTRIES (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'entrée
    list_id INTEGER NOT NULL,                   -- Clé étrangère vers READING_LISTS.id
    book_id INTEGER NOT NULL,                   -- Clé étrangère vers BOOKS.id
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date d'ajout du livre à la liste
    FOREIGN KEY (list_id) REFERENCES READING_LISTS(id)
        ON DELETE CASCADE,                      -- Si la liste est supprimée, les entrées aussi
    FOREIGN KEY (book_id) REFERENCES BOOKS(id)
        ON DELETE CASCADE,                      -- Si le livre est supprimé, il disparaît des listes
    UNIQUE(list_id, book_id)                    -- Contrainte: un livre ne peut être qu'une fois dans une liste
);

-- =============================================
-- Table: FAVORITES
-- Description: Table de jointure simple pour marquer les livres favoris d'un utilisateur.
-- =============================================
CREATE TABLE FAVORITES (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'entrée favori
    user_id INTEGER NOT NULL,                   -- Clé étrangère vers USERS.id
    book_id INTEGER NOT NULL,                   -- Clé étrangère vers BOOKS.id
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date d'ajout aux favoris
    FOREIGN KEY (user_id) REFERENCES USERS(id)
        ON DELETE CASCADE,                      -- Si l'utilisateur est supprimé, ses favoris aussi
    FOREIGN KEY (book_id) REFERENCES BOOKS(id)
        ON DELETE CASCADE,                      -- Si le livre est supprimé, il disparaît des favoris
    UNIQUE(user_id, book_id)                    -- Contrainte: un utilisateur ne favorise un livre qu'une fois
);

-- =============================================
-- Index pour améliorer les performances des requêtes courantes
-- (SQLite crée implicitement des index sur PK et UNIQUE, mais ceux-ci sont explicites)
-- =============================================
CREATE INDEX idx_books_title ON BOOKS(title);
CREATE INDEX idx_books_author ON BOOKS(author);
CREATE INDEX idx_books_genre_id ON BOOKS(genre_id);
CREATE INDEX idx_reviews_book_id ON REVIEWS(book_id);
CREATE INDEX idx_reviews_user_id ON REVIEWS(user_id);
CREATE INDEX idx_reading_lists_user_id ON READING_LISTS(user_id);
CREATE INDEX idx_book_list_entries_list_id ON BOOK_LIST_ENTRIES(list_id);
CREATE INDEX idx_book_list_entries_book_id ON BOOK_LIST_ENTRIES(book_id);
CREATE INDEX idx_favorites_user_id ON FAVORITES(user_id);
CREATE INDEX idx_favorites_book_id ON FAVORITES(book_id);
