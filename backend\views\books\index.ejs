<%- include('../partials/header') %>

<div class="container">
    <div class="page-header">
        <h1><%= currentGenre ? `Livres de ${currentGenre.name}` : 'Catalogue de livres' %></h1>
    </div>

    <!-- Messages Flash -->
    <%- include('../partials/flash-messages') %>

    <!-- Barre de recherche et filtres -->
    <div class="search-filter-container">
        <form action="/search" method="GET" class="search-form">
            <div class="search-input-container">
                <input type="search" name="q" placeholder="Rechercher un livre, un auteur..." value="<%= typeof searchQuery !== 'undefined' ? searchQuery : '' %>">
                <button type="submit"><i class="fas fa-search"></i></button>
            </div>

            <button type="button" class="toggle-filters">
                Filtres avancés <i class="fas fa-chevron-down"></i>
            </button>

            <div class="filters-container">
                <div class="filter-group">
                    <label for="genre_id">Genre</label>
                    <select name="genre_id" id="genre_id">
                        <option value="">Tous les genres</option>
                        <% genres.forEach(genre => { %>
                            <option value="<%= genre.id %>" <%= genre_id == genre.id ? 'selected' : '' %>><%= genre.name %></option>
                        <% }); %>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="min_rating">Note minimale</label>
                    <select name="min_rating" id="min_rating">
                        <option value="">Toutes les notes</option>
                        <option value="4" <%= min_rating == 4 ? 'selected' : '' %>>4★ et plus</option>
                        <option value="3" <%= min_rating == 3 ? 'selected' : '' %>>3★ et plus</option>
                        <option value="2" <%= min_rating == 2 ? 'selected' : '' %>>2★ et plus</option>
                        <option value="1" <%= min_rating == 1 ? 'selected' : '' %>>1★ et plus</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="year">Année</label>
                    <select name="year" id="year">
                        <option value="">Toutes les années</option>
                        <%
                        // Générer les options pour les années (de l'année actuelle à 1900)
                        const currentYear = new Date().getFullYear();
                        for (let y = currentYear; y >= 1900; y--) {
                        %>
                            <option value="<%= y %>" <%= year == y ? 'selected' : '' %>><%= y %></option>
                        <% } %>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="sort">Trier par</label>
                    <select name="sort" id="sort">
                        <option value="title" <%= sort === 'title' ? 'selected' : '' %>>Titre</option>
                        <option value="author" <%= sort === 'author' ? 'selected' : '' %>>Auteur</option>
                        <option value="average_rating" <%= sort === 'average_rating' ? 'selected' : '' %>>Note</option>
                        <option value="publication_year" <%= sort === 'publication_year' ? 'selected' : '' %>>Année</option>
                        <option value="created_at" <%= sort === 'created_at' ? 'selected' : '' %>>Date d'ajout</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="order">Ordre</label>
                    <select name="order" id="order">
                        <option value="ASC" <%= order === 'ASC' ? 'selected' : '' %>>Croissant</option>
                        <option value="DESC" <%= order === 'DESC' ? 'selected' : '' %>>Décroissant</option>
                    </select>
                </div>

                <button type="submit" class="filter-button">Filtrer</button>
            </div>
        </form>
    </div>

    <!-- Résultats -->
    <div class="results-info">
        <p><%= totalBooks %> livre<%= totalBooks !== 1 ? 's' : '' %> trouvé<%= totalBooks !== 1 ? 's' : '' %></p>
    </div>

    <!-- Grille de livres -->
    <div class="books-grid">
        <% if (books.length === 0) { %>
            <div class="empty-state">
                <p>Aucun livre ne correspond à votre recherche.</p>
                <a href="/books" class="btn btn-secondary">Voir tous les livres</a>
            </div>
        <% } else { %>
            <% books.forEach(book => { %>
                <div class="book-card">
                    <a href="/books/<%= book.id %>" class="book-link">
                        <div class="book-cover">
                            <img src="<%= book.cover_url || book.cover_image_url || '/images/placeholder-cover.svg' %>" alt="Couverture de <%= book.title %>">
                            <% if (new Date(book.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) { %>
                                <span class="book-badge">Nouveau</span>
                            <% } else if ((book.average_rating > 0 ? book.average_rating : book.initial_rating || 0) >= 4.5) { %>
                                <span class="book-badge">Top</span>
                            <% } %>
                        </div>
                        <div class="book-info">
                            <h3><%= book.title %></h3>
                            <p class="book-author"><%= book.author %></p>
                            <div class="book-meta">
                                <span class="book-genre"><%= book.genre ? book.genre.name : 'Non catégorisé' %></span>
                                <% if (book.publication_year) { %>
                                    <span class="book-year"><%= book.publication_year %></span>
                                <% } %>
                            </div>
                            <div class="book-rating">
                                <%
                                    // Calculer le rating à afficher : average_rating en priorité, sinon initial_rating
                                    // Les deux sont sur l'échelle 0-5
                                    let displayRating = 0;
                                    let ratingSource = '';
                                    if (book.average_rating > 0) {
                                        displayRating = book.average_rating;
                                        ratingSource = 'community';
                                    } else if (book.initial_rating > 0) {
                                        displayRating = book.initial_rating;
                                        ratingSource = 'initial';
                                    }
                                %>
                                <% if (displayRating > 0) { %>
                                    <% for (let i = 1; i <= 5; i++) { %>
                                        <% if (i <= Math.round(displayRating)) { %>
                                            <span class="star filled">★</span>
                                        <% } else { %>
                                            <span class="star">☆</span>
                                        <% } %>
                                    <% } %>
                                    <% if (ratingSource === 'initial') { %>
                                        <span class="rating-source">(OpenLibrary)</span>
                                    <% } %>
                                <% } else { %>
                                    <span class="no-rating">Pas encore noté</span>
                                <% } %>
                            </div>
                        </div>
                    </a>
                </div>
            <% }); %>
        <% } %>
    </div>

    <!-- Pagination -->
    <% if (totalPages > 1) { %>
        <div class="pagination">
            <% if (currentPage > 1) { %>
                <a href="?page=<%= currentPage - 1 %>&sort=<%= sort %>&order=<%= order %>&genre_id=<%= genre_id %>&min_rating=<%= min_rating %>&year=<%= year %>" class="pagination-link prev">&laquo; Précédent</a>
            <% } %>

            <% for (let i = 1; i <= totalPages; i++) { %>
                <a href="?page=<%= i %>&sort=<%= sort %>&order=<%= order %>&genre_id=<%= genre_id %>&min_rating=<%= min_rating %>&year=<%= year %>" class="pagination-link <%= i === currentPage ? 'active' : '' %>"><%= i %></a>
            <% } %>

            <% if (currentPage < totalPages) { %>
                <a href="?page=<%= currentPage + 1 %>&sort=<%= sort %>&order=<%= order %>&genre_id=<%= genre_id %>&min_rating=<%= min_rating %>&year=<%= year %>" class="pagination-link next">Suivant &raquo;</a>
            <% } %>
        </div>
    <% } %>
</div>

<%- include('../partials/footer') %>
