/**
 * <PERSON>ript pour créer les listes de lecture par défaut pour tous les utilisateurs
 * 
 * Usage: node backend/scripts/create-default-lists.js
 */

import { User, ReadingList } from '../models/index.js';
import '../config/env.js';

async function createDefaultListsForAllUsers() {
  try {
    console.log('Récupération de tous les utilisateurs...');
    const users = await User.findAll();
    console.log(`${users.length} utilisateurs trouvés.`);

    for (const user of users) {
      // Vérifier si l'utilisateur a déjà des listes
      const listCount = await ReadingList.count({
        where: { user_id: user.id }
      });

      if (listCount === 0) {
        console.log(`Création des listes par défaut pour l'utilisateur ${user.id} (${user.username})...`);
        await ReadingList.createDefaultLists(user.id);
        console.log(`Listes par défaut créées pour l'utilisateur ${user.id} (${user.username}).`);
      } else {
        console.log(`L'utilisateur ${user.id} (${user.username}) a déjà ${listCount} listes.`);
      }
    }

    console.log('Opération terminée avec succès.');
    process.exit(0);
  } catch (error) {
    console.error('Erreur lors de la création des listes par défaut:', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
createDefaultListsForAllUsers();
