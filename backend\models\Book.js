import { Model, DataTypes, Op } from 'sequelize';
import sequelize from '../config/db.js';

class Book extends Model {
  /**
   * Génère un slug à partir des informations du livre
   * @param {Object} bookData - Données du livre
   * @returns {string} - Slug généré
   */
  static generateSlug(bookData) {
    const { title, author, language, publication_year } = bookData;

    // Créer une version de base du slug
    let baseSlug = `${title}-${author}`;
    // Inclure la langue dans le slug si elle est spécifiée, quelle qu'elle soit
    if (language) {
      baseSlug += `-${language}`;
    }
    if (publication_year) {
      baseSlug += `-${publication_year}`;
    }

    // Convertir en minuscules et remplacer les caractères spéciaux par des tirets
    let slug = baseSlug
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    return slug;
  }

  /**
   * Recherche des livres par titre ou auteur avec filtres avancés
   * @param {string} searchTerm - Terme de recherche
   * @param {Object} filters - Filtres (genre_id, min_rating, publication_year)
   * @param {Object} options - Options de pagination et tri
   * @returns {Promise<Object>} - Objet contenant les livres et le nombre total
   */
  static async search(searchTerm, filters = {}, options = {}) {
    const { limit = 20, offset = 0, orderBy = 'title', order = 'ASC', includeAllStatuses = false } = options;

    // Construction des conditions de recherche
    const whereConditions = {
      [Op.or]: [
        { title: { [Op.like]: `%${searchTerm}%` } },
        { author: { [Op.like]: `%${searchTerm}%` } },
        { summary: { [Op.like]: `%${searchTerm}%` } },
        { description: { [Op.like]: `%${searchTerm}%` } }
      ]
    };

    // Par défaut, n'afficher que les livres publiés (sauf si explicitement demandé)
    if (!includeAllStatuses) {
      whereConditions.status = 'published';
    }

    // Ajouter les filtres supplémentaires
    if (filters.genre_id) {
      whereConditions.genre_id = filters.genre_id;
    }

    if (filters.min_rating) {
      whereConditions.average_rating = { [Op.gte]: filters.min_rating };
    }

    if (filters.publication_year) {
      whereConditions.publication_year = filters.publication_year;
    }

    // Filtre par statut si spécifié
    if (filters.status) {
      whereConditions.status = filters.status;
    }

    try {
      // Utiliser findAndCountAll pour obtenir à la fois les résultats et le nombre total
      const result = await Book.findAndCountAll({
        where: whereConditions,
        order: [[orderBy, order]],
        limit,
        offset,
        include: [{ model: sequelize.models.Genre, as: 'genre' }]
      });

      return {
        books: result.rows,
        total: result.count
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les livres avec filtres avancés
   * @param {Object} filters - Filtres (genre_id, min_rating, publication_year)
   * @param {Object} options - Options de pagination et tri
   * @returns {Promise<Object>} - Objet contenant les livres et le nombre total
   */
  static async findFiltered(filters = {}, options = {}) {
    const { limit = 20, offset = 0, orderBy = 'title', order = 'ASC', includeAllStatuses = false } = options;

    // Construction des conditions de filtrage
    const whereConditions = {};

    // Par défaut, n'afficher que les livres publiés (sauf si explicitement demandé)
    if (!includeAllStatuses) {
      whereConditions.status = 'published';
    }

    // Ajouter les filtres
    if (filters.genre_id) {
      whereConditions.genre_id = filters.genre_id;
    }

    if (filters.min_rating) {
      whereConditions.average_rating = { [Op.gte]: filters.min_rating };
    }

    if (filters.publication_year) {
      whereConditions.publication_year = filters.publication_year;
    }

    // Filtre par statut si spécifié
    if (filters.status) {
      whereConditions.status = filters.status;
    }

    try {
      // Utiliser findAndCountAll pour obtenir à la fois les résultats et le nombre total
      const result = await Book.findAndCountAll({
        where: whereConditions,
        order: [[orderBy, order]],
        limit,
        offset,
        include: [{ model: sequelize.models.Genre, as: 'genre' }]
      });

      return {
        books: result.rows,
        total: result.count
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les livres par genre
   * @param {number} genreId - ID du genre
   * @param {Object} options - Options de pagination et tri
   * @returns {Promise<Object>} - Objet contenant les livres et le nombre total
   */
  static async findByGenre(genreId, options = {}) {
    return await Book.findFiltered({ genre_id: genreId }, options);
  }

  /**
   * Récupère les livres récents
   * @param {number} limit - Nombre de livres à récupérer
   * @param {boolean} includeAllStatuses - Inclure tous les statuts ou seulement les publiés
   * @returns {Promise<Array>} - Liste des livres récents
   */
  static async findRecent(limit = 10, includeAllStatuses = false) {
    const whereConditions = includeAllStatuses ? {} : { status: 'published' };

    try {
      return await Book.findAll({
        where: whereConditions,
        order: [['created_at', 'DESC']],
        limit,
        include: [{ model: sequelize.models.Genre, as: 'genre' }]
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Récupère les livres les mieux notés
   * @param {number} limit - Nombre de livres à récupérer
   * @param {boolean} includeAllStatuses - Inclure tous les statuts ou seulement les publiés
   * @returns {Promise<Array>} - Liste des livres les mieux notés
   */
  static async findTopRated(limit = 10, includeAllStatuses = false) {
    const whereConditions = {
      [Op.or]: [
        { average_rating: { [Op.gt]: 0 } },
        { initial_rating: { [Op.gt]: 0 } }
      ]
    };

    if (!includeAllStatuses) {
      whereConditions.status = 'published';
    }

    try {
      return await Book.findAll({
        where: whereConditions,
        attributes: {
          include: [
            // Calculer un rating combiné : utiliser average_rating s'il existe, sinon initial_rating
            // Maintenant les deux sont sur l'échelle 0-5
            [
              sequelize.literal(`
                CASE
                  WHEN average_rating > 0 THEN average_rating
                  WHEN initial_rating > 0 THEN initial_rating
                  ELSE 0
                END
              `),
              'computed_rating'
            ]
          ]
        },
        order: [
          [sequelize.literal('computed_rating'), 'DESC'],
          ['created_at', 'DESC'] // Tri secondaire par date de création
        ],
        limit,
        include: [{ model: sequelize.models.Genre, as: 'genre' }]
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Met à jour la note moyenne d'un livre
   * @param {number} bookId - ID du livre
   * @returns {Promise<Object>} - Le livre mis à jour
   */
  static async updateAverageRating(bookId) {
    try {
      // Récupérer toutes les notes pour ce livre
      const reviews = await sequelize.models.Review.findAll({
        where: { book_id: bookId }
      });

      // Calculer la moyenne
      let averageRating = 0;
      if (reviews.length > 0) {
        const sum = reviews.reduce((total, review) => total + review.rating, 0);
        averageRating = sum / reviews.length;
      }

      // Mettre à jour le livre
      await Book.update(
        { average_rating: averageRating },
        { where: { id: bookId } }
      );

      return await Book.findByPk(bookId);
    } catch (error) {
      throw error;
    }
  }
}

// Définition du modèle
Book.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    author: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isbn: {
      type: DataTypes.STRING,
      allowNull: true
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    cover_image_url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    // Getter virtuel pour l'URL de couverture
    cover_url: {
      type: DataTypes.VIRTUAL,
      get() {
        if (!this.cover_image_url) {
          return null;
        }

        // Si l'URL commence par http, c'est une URL externe
        if (this.cover_image_url.startsWith('http')) {
          return this.cover_image_url;
        }

        // Sinon, c'est un chemin relatif, on le retourne tel quel
        // Le middleware express.static s'occupera de servir le fichier
        return this.cover_image_url;
      }
    },
    publication_year: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    pdf_url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    genre_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Genres',
        key: 'id'
      }
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    initial_rating: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    average_rating: {
      type: DataTypes.FLOAT,
      allowNull: true,
      defaultValue: 0
    },
    language: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'fr'
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'draft',
      validate: {
        isIn: [['draft', 'published']]
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    sequelize,
    modelName: 'Book',
    tableName: 'BOOKS',
    timestamps: true,
    underscored: true,
    hooks: {
      beforeCreate: (book) => {
        // Générer le slug avant la création
        if (!book.slug) {
          book.slug = Book.generateSlug({
            title: book.title,
            author: book.author,
            language: book.language,
            publication_year: book.publication_year
          });
        }
      },
      beforeUpdate: (book) => {
        // Mettre à jour le slug si les données pertinentes ont changé
        if (book.changed('title') || book.changed('author') ||
            book.changed('language') || book.changed('publication_year')) {
          book.slug = Book.generateSlug({
            title: book.title,
            author: book.author,
            language: book.language,
            publication_year: book.publication_year
          });
        }
      }
    }
    // Les getters sont maintenant définis dans les attributs du modèle
  }
);

export default Book;
