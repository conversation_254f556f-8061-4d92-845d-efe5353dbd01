<%- include('layout', {
    path: '/dashboard/reading-lists/' + readingList.id,
    success: typeof success !== 'undefined' ? success : '',
    error: typeof error !== 'undefined' ? error : '',
    info: typeof info !== 'undefined' ? info : '',
    body: `
<div class="reader-page-header">
    <h1>${readingList.list_name}</h1>
    <div class="reader-page-actions">
        <a href="/dashboard/reading-lists" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour aux listes
        </a>
        ${!readingList.is_default ? `
            <button class="btn btn-text rename-list" data-list-id="${readingList.id}" data-list-name="${readingList.list_name}">
                <i class="fas fa-edit"></i> Renommer
            </button>
            <button class="btn btn-text delete-list" data-list-id="${readingList.id}">
                <i class="fas fa-trash"></i> Supprimer
            </button>
        ` : ''}
    </div>
</div>

<div class="reader-content-card">
    <div class="reader-card-header">
        <h2>Livres dans cette liste</h2>
        <a href="/books" class="btn btn-primary">
            <i class="fas fa-plus"></i> Ajouter des livres
        </a>
    </div>

    <div class="search-filter-container">
        <div class="search-form">
            <div class="search-input-container">
                <input type="text" id="search-list-${readingList.id}" class="search-input" placeholder="Rechercher dans cette liste...">
                <button class="search-btn" data-list-id="${readingList.id}">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <button type="button" class="toggle-filters" data-list-id="${readingList.id}">
                Filtres avancés <i class="fas fa-chevron-down"></i>
            </button>

            <div class="filters-container" id="filters-list-${readingList.id}">
                <div class="filter-group">
                    <label for="sort-list-${readingList.id}">Trier par</label>
                    <select id="sort-list-${readingList.id}" class="sort-select" data-list-id="${readingList.id}">
                        <option value="title_asc">Titre (A-Z)</option>
                        <option value="title_desc">Titre (Z-A)</option>
                        <option value="author_asc">Auteur (A-Z)</option>
                        <option value="author_desc">Auteur (Z-A)</option>
                        <option value="date_added_desc">Date d'ajout (récent)</option>
                        <option value="date_added_asc">Date d'ajout (ancien)</option>
                    </select>
                </div>

                <button type="button" class="filter-button apply-filters" data-list-id="${readingList.id}">Appliquer</button>
            </div>
        </div>
    </div>

    <div id="list-books-container">
        ${readingList.books && readingList.books.length > 0 ? `
            <div class="books-grid">
                ${readingList.books.map(book => `
                    <div class="book-card">
                        <a href="/books/${book.id}" class="book-link">
                            <div class="book-cover">
                                <img src="${book.cover_image_url || '/frontend/images/placeholder-cover.svg'}" alt="Couverture de ${book.title}">
                            </div>
                            <div class="book-info">
                                <h4>${book.title}</h4>
                                <p class="book-author">${book.author}</p>
                                ${book.genre ? `<p class="book-genre">${book.genre.name}</p>` : ''}
                            </div>
                        </a>
                        <button class="btn btn-text remove-from-list" data-list-id="${readingList.id}" data-book-id="${book.id}">
                            <i class="fas fa-times"></i> Retirer
                        </button>
                    </div>
                `).join('')}
            </div>
        ` : `
            <div class="empty-state">
                <p>Aucun livre dans cette liste.</p>
                <a href="/books" class="btn btn-secondary">Explorer le catalogue</a>
            </div>
        `}
    </div>
</div>

<!-- Modal pour renommer une liste -->
<div class="modal" id="listModal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3 id="modalTitle">Renommer la liste</h3>
        <form id="listForm">
            <input type="hidden" id="listId" value="${readingList.id}">
            <div class="form-group">
                <label for="listName">Nom de la liste</label>
                <input type="text" id="listName" name="listName" value="${readingList.list_name}" required>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Enregistrer</button>
                <button type="button" class="btn btn-secondary" id="cancelListBtn">Annuler</button>
            </div>
        </form>
    </div>
</div>
` }) %>
