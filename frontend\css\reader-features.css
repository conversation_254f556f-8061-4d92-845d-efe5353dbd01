/**
 * Styles pour les fonctionnalités de lecteur (Phase 4)
 * - Listes de lecture
 * - Favoris
 * - Avis
 */

/* ===== DASHBOARD TABS ===== */
.dashboard-tabs {
    margin-top: 2rem;
}

.tabs-header {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.tab-button {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-weight: 600;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== READING LISTS ===== */
.reading-lists-container {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.reading-lists-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.reading-lists-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 1rem;
}

.tab-btn {
    padding: 0.75rem 1.25rem;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    background-color: var(--bg-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tab-content {
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.tab-content.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: fadeIn 0.5s ease;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.list-title-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.list-title-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.list-actions {
    display: flex;
    gap: 0.5rem;
}

.reader-page-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.view-all-link {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* ===== FAVORITES ===== */
.favorites-container {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

/* ===== REVIEWS ===== */
.reviews-container {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.reviews-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--bg-light);
    border-radius: 8px;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.rating-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.rating-filter-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    cursor: pointer;
}

.rating-filter-item input {
    margin-right: 0.25rem;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.review-card {
    background-color: var(--bg-light);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.review-user {
    font-weight: 600;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.review-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.star {
    color: #ccc;
    font-size: 1.2rem;
}

.star.filled {
    color: #FFD700;
}

.review-content {
    margin-top: 0.5rem;
}

.review-text {
    line-height: 1.5;
}

.review-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.text-muted {
    color: var(--text-muted);
    font-style: italic;
}

/* ===== BOOK DETAILS PAGE ===== */
.book-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin: 1.25rem 0;
}

.book-actions .btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.book-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.book-actions .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.book-actions .btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.book-reviews {
    margin-top: 2rem;
}

.reviews-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.page-btn {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background-color: var(--bg-hover);
}

.page-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== MODALS ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

.close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    color: var(--primary-color);
    background-color: var(--bg-light);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

/* Rating input */
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input {
    display: none;
}

.rating-input label {
    font-size: 1.5rem;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #FFD700;
}

/* Reading lists select */
.reading-lists-select {
    max-height: 300px;
    overflow-y: auto;
}

.list-select-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.list-select-item:last-child {
    border-bottom: none;
}

/* Loading spinner */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: var(--text-muted);
}

.loading-spinner i {
    margin-right: 0.5rem;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 2.5rem;
    color: var(--text-muted);
    background-color: var(--bg-light);
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px dashed var(--border-color);
    transition: all 0.3s ease;
}

.empty-state:hover {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.empty-state p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.empty-state .btn {
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Error message */
.error-message {
    color: var(--error-color);
    text-align: center;
    padding: 1rem;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
    .tabs-header {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1;
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .book-actions {
        flex-direction: column;
    }

    .book-actions button {
        width: 100%;
    }

    .modal-content {
        margin: 20% auto;
        padding: 1.5rem;
    }
}

/* Small books grid for dashboard */
.books-grid.small {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1.25rem;
}

.books-grid.small .book-card {
    margin-bottom: 0;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.books-grid.small .book-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.books-grid.small .book-cover {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.books-grid.small .book-cover img {
    transition: transform 0.5s ease;
}

.books-grid.small .book-card:hover .book-cover img {
    transform: scale(1.05);
}

.books-grid.small .book-info {
    padding: 0.75rem;
    background-color: white;
}

.books-grid.small .book-info h4 {
    font-size: 0.9rem;
    margin: 0 0 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.books-grid.small .book-info p {
    font-size: 0.8rem;
    margin: 0;
    color: var(--text-muted);
}

/* Book cover small for reviews */
.book-cover-small {
    width: 60px;
    height: 90px;
    overflow: hidden;
    border-radius: 4px;
}

.book-cover-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.book-info-small {
    margin-left: 1rem;
}

.book-info-small h4 {
    font-size: 1rem;
    margin: 0 0 0.25rem;
}

.book-info-small p {
    font-size: 0.8rem;
    margin: 0;
}

.review-book {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

/* Active state for buttons */
.btn.active {
    background-color: var(--primary-dark);
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Style spécifique pour le bouton favoris actif */
#favoriteBtn.active {
    background-color: #e74c3c !important; /* Rouge pour le cœur */
    color: white !important;
    border-color: #e74c3c !important;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3) !important;
}

#favoriteBtn.active:hover {
    background-color: #c0392b !important;
    transform: translateY(-2px);
}

/* Style pour le cœur plein */
#favoriteBtn.active i {
    color: white !important;
}

/* Animations pour les transitions de page */
.page-loaded .reader-content-card,
.page-loaded .dashboard-tabs,
.page-loaded .reading-lists-container,
.page-loaded .favorites-container,
.page-loaded .reviews-container {
    animation: slideInUp 0.5s ease;
}

.animate-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Effet de survol pour les boutons */
.btn-hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Effet d'ondulation pour les clics */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.6s linear;
    transform: scale(0);
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Amélioration des boutons d'action */
.reader-action-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.reader-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.reader-action-card i {
    transition: all 0.3s ease;
}

.reader-action-card:hover i {
    transform: scale(1.2);
}
