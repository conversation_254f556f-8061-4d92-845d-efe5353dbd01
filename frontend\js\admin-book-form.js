/**
 * Script pour le formulaire d'administration des livres
 * Gère la mise à jour automatique du slug et la prévisualisation des images
 */

document.addEventListener('DOMContentLoaded', function() {
    // Éléments du formulaire
    const titleInput = document.getElementById('title');
    const authorInput = document.getElementById('author');
    const languageInput = document.getElementById('language');
    const yearInput = document.getElementById('publication_year');
    const slugInput = document.getElementById('slug');
    const coverUrlInput = document.getElementById('cover_image_url');
    const coverPreview = document.getElementById('cover-preview');
    
    // Fonction pour générer un slug
    function generateSlug(title, author, language, year) {
        if (!title) return '';
        
        // Normaliser les chaînes
        const normalizedTitle = title.toLowerCase().trim();
        const normalizedAuthor = author ? author.toLowerCase().trim() : '';
        const normalizedLanguage = language || 'fr';
        const normalizedYear = year || '';
        
        // Générer les parties du slug
        let slugParts = [];
        
        // Ajouter le titre normalisé
        if (normalizedTitle) {
            slugParts.push(normalizedTitle);
        }
        
        // Ajouter la langue si différente de 'fr'
        if (normalizedLanguage && normalizedLanguage !== 'fr') {
            slugParts.push(normalizedLanguage);
        }
        
        // Ajouter l'auteur
        if (normalizedAuthor) {
            slugParts.push(normalizedAuthor);
        }
        
        // Ajouter l'année
        if (normalizedYear) {
            slugParts.push(normalizedYear);
        }
        
        // Joindre les parties avec des tirets
        let slug = slugParts.join('-');
        
        // Remplacer les caractères spéciaux et les espaces
        slug = slug.replace(/[^a-z0-9-]/g, '-');
        
        // Remplacer les tirets multiples par un seul
        slug = slug.replace(/-+/g, '-');
        
        // Supprimer les tirets au début et à la fin
        slug = slug.replace(/^-+|-+$/g, '');
        
        return slug;
    }
    
    // Fonction pour mettre à jour le slug
    function updateSlug() {
        const title = titleInput.value;
        const author = authorInput.value;
        const language = languageInput.value;
        const year = yearInput.value;
        
        const slug = generateSlug(title, author, language, year);
        slugInput.value = slug;
    }
    
    // Fonction pour mettre à jour la prévisualisation de l'image
    function updateCoverPreview() {
        const url = coverUrlInput.value.trim();
        
        if (url) {
            // Afficher la prévisualisation
            coverPreview.innerHTML = `<img src="${url}" alt="Prévisualisation de la couverture">`;
            coverPreview.style.display = 'block';
        } else {
            // Masquer la prévisualisation
            coverPreview.innerHTML = '';
            coverPreview.style.display = 'none';
        }
    }
    
    // Ajouter les écouteurs d'événements pour la mise à jour du slug
    if (titleInput && authorInput && languageInput && yearInput && slugInput) {
        titleInput.addEventListener('input', updateSlug);
        authorInput.addEventListener('input', updateSlug);
        languageInput.addEventListener('change', updateSlug);
        yearInput.addEventListener('input', updateSlug);
        
        // Mise à jour initiale du slug
        updateSlug();
    }
    
    // Ajouter l'écouteur d'événement pour la prévisualisation de l'image
    if (coverUrlInput && coverPreview) {
        coverUrlInput.addEventListener('input', updateCoverPreview);
        
        // Mise à jour initiale de la prévisualisation
        updateCoverPreview();
    }
});
