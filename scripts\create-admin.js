#!/usr/bin/env node

/**
 * Script pour créer un utilisateur administrateur dans la base de données
 * Usage: node scripts/create-admin.js <username> <email> <password>
 */

import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import bcrypt from 'bcrypt';
import db from '../backend/config/database.js';
import readline from 'readline';

// --- Calculer __dirname ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// --- Charger .env ---
const envPath = path.resolve(__dirname, '..', '.env');
dotenv.config({ path: envPath });

// Interface pour lire les entrées utilisateur
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Fonction pour valider l'email
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Fonction pour créer un administrateur
 */
async function createAdmin(username, email, password) {
    return new Promise((resolve, reject) => {
        // Vérifier si l'utilisateur existe déjà
        db.get('SELECT * FROM USERS WHERE username = ? OR email = ?', [username, email], (err, user) => {
            if (err) {
                return reject(`Erreur de base de données: ${err.message}`);
            }

            if (user) {
                return reject(`Un utilisateur avec ce nom d'utilisateur ou cet email existe déjà.`);
            }

            // Hacher le mot de passe
            bcrypt.hash(password, 10, (err, hash) => {
                if (err) {
                    return reject(`Erreur lors du hachage du mot de passe: ${err.message}`);
                }

                // Insérer l'utilisateur admin
                const sql = 'INSERT INTO USERS (username, email, password_hash, role) VALUES (?, ?, ?, ?)';
                db.run(sql, [username, email, hash, 'admin'], function(err) {
                    if (err) {
                        return reject(`Erreur lors de la création de l'administrateur: ${err.message}`);
                    }

                    resolve({
                        id: this.lastID,
                        username,
                        email,
                        role: 'admin'
                    });
                });
            });
        });
    });
}

/**
 * Fonction principale
 */
async function main() {
    try {
        // Récupérer les arguments de la ligne de commande
        const args = process.argv.slice(2);
        let username, email, password;

        if (args.length === 3) {
            // Si tous les arguments sont fournis en ligne de commande
            [username, email, password] = args;
            
            if (!isValidEmail(email)) {
                console.error('Erreur: Format d\'email invalide.');
                process.exit(1);
            }
        } else {
            // Sinon, demander interactivement
            username = await new Promise(resolve => {
                rl.question('Nom d\'utilisateur: ', answer => resolve(answer.trim()));
            });

            email = await new Promise(resolve => {
                rl.question('Email: ', answer => {
                    const trimmedEmail = answer.trim();
                    if (!isValidEmail(trimmedEmail)) {
                        console.error('Erreur: Format d\'email invalide. Veuillez réessayer.');
                        return resolve('');
                    }
                    return resolve(trimmedEmail);
                });
            });

            // Si l'email est invalide, redemander
            while (!email) {
                email = await new Promise(resolve => {
                    rl.question('Email (format valide): ', answer => {
                        const trimmedEmail = answer.trim();
                        if (!isValidEmail(trimmedEmail)) {
                            console.error('Erreur: Format d\'email invalide. Veuillez réessayer.');
                            return resolve('');
                        }
                        return resolve(trimmedEmail);
                    });
                });
            }

            password = await new Promise(resolve => {
                rl.question('Mot de passe: ', answer => resolve(answer.trim()));
            });

            // Vérifier que le mot de passe est assez fort
            if (password.length < 8) {
                console.error('Erreur: Le mot de passe doit contenir au moins 8 caractères.');
                process.exit(1);
            }
        }

        // Créer l'administrateur
        const admin = await createAdmin(username, email, password);
        console.log(`✅ Administrateur créé avec succès:`);
        console.log(`   ID: ${admin.id}`);
        console.log(`   Nom d'utilisateur: ${admin.username}`);
        console.log(`   Email: ${admin.email}`);
        console.log(`   Rôle: ${admin.role}`);
        console.log(`\nVous pouvez maintenant vous connecter à l'interface d'administration.`);
    } catch (error) {
        console.error(`❌ Erreur: ${error}`);
        process.exit(1);
    } finally {
        rl.close();
        db.close();
    }
}

// Exécuter le script
main();
