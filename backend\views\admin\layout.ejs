<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof title !== 'undefined' ? title : 'Administration - Nookli' %></title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/admin.css">
    <link rel="stylesheet" href="/css/admin-book-form.css">
    <!-- Icônes Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="admin-body">
    <header class="admin-header">
        <div class="container">
            <nav>
                <a class="brand" href="/admin">
                    <i class="fas fa-book-reader"></i> <PERSON><PERSON>li Admin
                </a>

                <ul class="nav-links">
                    <li><a href="/" target="_blank">Voir le site</a></li>
                    <li>
                        <form action="/logout" method="POST" style="display: inline;">
                            <button type="submit" class="logout-button">Déconnexion</button>
                        </form>
                    </li>
                </ul>

                <!-- Bouton menu mobile -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Menu">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <div class="admin-container">
        <aside class="admin-sidebar">
            <button class="admin-sidebar-toggle" id="admin-sidebar-toggle" title="Réduire/Agrandir le menu">
                <i class="fas fa-chevron-left"></i>
            </button>

            <div class="admin-user">
                <div class="admin-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="admin-user-info">
                    <p class="admin-username"><%= currentUser.username %></p>
                    <p class="admin-role">Administrateur</p>
                </div>
            </div>

            <nav class="admin-nav">
                <ul>
                    <li>
                        <a href="/admin" class="<%= path === '/admin' ? 'active' : '' %>">
                            <i class="fas fa-tachometer-alt"></i> <span class="nav-text">Tableau de bord</span>
                        </a>
                    </li>
                    <li>
                        <a href="/admin/books" class="<%= path && path.startsWith('/admin/books') ? 'active' : '' %>">
                            <i class="fas fa-book"></i> <span class="nav-text">Livres</span>
                        </a>
                    </li>
                    <li>
                        <a href="/admin/genres" class="<%= path && path.startsWith('/admin/genres') ? 'active' : '' %>">
                            <i class="fas fa-tags"></i> <span class="nav-text">Genres</span>
                        </a>
                    </li>
                    <li>
                        <a href="/admin/users" class="<%= path && path.startsWith('/admin/users') ? 'active' : '' %>">
                            <i class="fas fa-users"></i> <span class="nav-text">Utilisateurs</span>
                        </a>
                    </li>
                    <!-- Lien vers les listes de lecture supprimé car géré dans le dashboard lecteur -->
                    <li>
                        <a href="/admin/stats" class="<%= path === '/admin/stats' ? 'active' : '' %>">
                            <i class="fas fa-chart-bar"></i> <span class="nav-text">Statistiques</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="admin-main">
            <%- include('../partials/flash-messages') %>

            <%- body %>
        </main>
    </div>

    <footer class="admin-footer">
        <div class="container">
            <p>© <%= new Date().getFullYear() %> Nookli - Interface d'administration</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/admin.js"></script>
</body>
</html>
