/**
 * Configuration unifiée de la base de données
 * Ce module centralise toute la configuration de la base de données
 * et expose les fonctions nécessaires pour interagir avec elle.
 */

import { Sequelize } from 'sequelize';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { ENV } from './env.js';

// --- Calculer __dirname ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Chemin vers la base de données SQLite
const DB_PATH = ENV.DATABASE_PATH;
// Déterminer si le chemin est absolu ou relatif
const dbPath = path.isAbsolute(DB_PATH)
  ? DB_PATH
  : path.join(process.cwd(), DB_PATH);

// Vérifier si le répertoire existe, sinon le créer
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

console.log(`[DB] Utilisation de la base de données: ${dbPath}`);

// Créer une instance Sequelize
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: dbPath,
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  define: {
    // Ajouter automatiquement les timestamps (createdAt, updatedAt)
    timestamps: true,
    // Utiliser le snake_case pour les noms de colonnes
    underscored: true,
    // Ne pas pluraliser les noms de tables
    freezeTableName: false,
  }
});

/**
 * Teste la connexion à la base de données
 * @returns {Promise<boolean>} - true si la connexion est établie, false sinon
 */
export const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('[DB] Connexion à la base de données établie avec succès.');
    return true;
  } catch (error) {
    console.error('[DB] Impossible de se connecter à la base de données:', error);
    return false;
  }
};

/**
 * Synchronise les modèles avec la base de données
 * @param {Object} options - Options de synchronisation
 * @returns {Promise<boolean>} - true si la synchronisation est réussie, false sinon
 */
export const syncDatabase = async (options = {}) => {
  try {
    await sequelize.sync(options);
    console.log('[DB] Base de données synchronisée avec succès.');
    return true;
  } catch (error) {
    console.error('[DB] Erreur lors de la synchronisation de la base de données:', error);
    return false;
  }
};

/**
 * Initialise la base de données avec les modèles
 * @param {Object} models - Les modèles à synchroniser
 * @returns {Promise<boolean>} - true si l'initialisation est réussie, false sinon
 */
export const initDatabase = async (models) => {
  try {
    // Tester la connexion
    const connected = await testConnection();
    if (!connected) {
      return false;
    }

    // Synchroniser les modèles
    const synced = await syncDatabase();
    if (!synced) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('[DB] Erreur lors de l\'initialisation de la base de données:', error);
    return false;
  }
};

export default sequelize;
