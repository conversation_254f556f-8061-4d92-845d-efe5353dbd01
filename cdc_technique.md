## Document de Spécification Complet : <PERSON><PERSON><PERSON> - Mon Coin Lecture

### Table des Matières

1.  Introduction et Vision du Projet
2.  Types d'Utilisateurs
3.  Fonctionnalités Clés
4.  Exigences Fonctionnelles
5.  Exigences Non Fonctionnelles
6.  User Flows / Stories Principales
7.  Technologie Utilisée
8.  Structure du Projet
9.  Chronogramme Général de Développement
10. Schéma de la Base de Données (SQL Finalisé)

---

### 1. Introduction et Vision du Projet

**Nom du Projet :** Nookli - Mon Coin Lecture
**Slogan :** Li<PERSON>z, suivez, découvrez : tous vos livres et histoires au même endroit.

**Description Générale :**
Nook<PERSON> est une application web conçue initialement comme un projet scolaire. Elle vise à offrir aux passionnés de lecture un espace centralisé et intuitif pour gérer leurs bibliothèques personnelles, suivre leur progression de lecture (livres lus, en cours, à lire), découvrir de nouveaux titres et partager leurs avis. L'application met l'accent sur une page d'accueil (landing page) attrayante et informative pour engager les nouveaux visiteurs, une simplicité d'utilisation générale, une interface claire et une expérience utilisateur agréable, développée selon une approche **"Mobile First"**.

**Objectifs Principaux :**

*   Accueillir les visiteurs avec une page de destination engageante facilitant la découverte et la recherche immédiate.
*   Présenter clairement les fonctionnalités et avantages de l'application aux nouveaux utilisateurs.
*   Fournir une plateforme intuitive pour cataloguer et organiser les livres lus, en cours de lecture ou à lire via des listes personnalisables et des favoris.
*   Permettre aux utilisateurs de noter les livres (1-5 étoiles) et de partager un avis unique et modifiable par livre.
*   Faciliter la découverte de livres via la recherche (titre/auteur), le filtrage par genre et des sections de livres mis en avant sur la page d'accueil.
*   Offrir une interface d'administration simple pour gérer le catalogue de livres, les genres et modérer les avis des utilisateurs.
*   Servir de projet d'apprentissage pratique en utilisant Node.js, Express, EJS, SQLite, et des pratiques JavaScript modernes (ES6+).
*   **Note :** La fonctionnalité de lecture de PDF en ligne est envisagée pour etre realise en derniere position.

### 2. Types d'Utilisateurs

1.  **Visiteur (Anonyme) :** Utilisateur non connecté. Accède à la page d'accueil/destination. Peut rechercher et explorer le catalogue, consulter les détails des livres (y compris les avis existants) et voir les sections de livres mis en avant. Incité à s'inscrire pour accéder aux fonctionnalités complètes.
2.  **Lecteur (Utilisateur Enregistré) :** Utilisateur disposant d'un compte et connecté. A accès à toutes les fonctionnalités visiteur, plus la gestion de son profil, la création et gestion de listes de lecture (par défaut et personnalisées), la gestion de ses favoris, et la possibilité de soumettre, modifier et supprimer son propre avis unique sur les livres.
3.  **Administrateur :** Utilisateur avec des privilèges étendus (`role='admin'`). A accès à toutes les fonctionnalités lecteur, plus un panneau d'administration dédié pour :
    *   Gérer le catalogue de livres (Ajouter, Modifier, Supprimer).
    *   Gérer les genres (Ajouter, Modifier, Supprimer).
    *   Modérer les avis des utilisateurs (Visualiser et Supprimer).

### 3. Fonctionnalités Clés

*   **Page d'Accueil / Destination (Landing Page) :**
    *   Section "Hero" visuellement attrayante avec barre de recherche multi-critères.
    *   Présentation claire et concise des fonctionnalités et avantages de Nookli.
    *   Sections dynamiques mettant en avant des livres (ex: "Les plus populaires", "Les mieux notés", "Ajouts récents" - basé sur des critères simples pour V1).
    *   Appel à l'action (CTA) visible pour l'inscription ou la connexion.
*   **Gestion de Compte :**
    *   Inscription sécurisée (nom d'utilisateur, email, mot de passe haché).
    *   Connexion / Déconnexion sécurisée via Passport.js.
    *   Gestion de session persistante.
    *   (Optionnel V1) Page de profil basique pour voir ses informations.
*   **Catalogue de Livres :**
    *   Affichage de listes de livres (accueil, résultats de recherche, par genre).
    *   Page de détails complète pour chaque livre (couverture, titre, auteur, ISBN, description, année, genre, note moyenne calculée, liste des avis).
    *   Fonction de recherche (par titre et/ou auteur).
    *   Fonction de filtrage par genre(s).
*   **Suivi de Lecture (Lecteur) :**
    *   Création automatique des listes par défaut ("À lire", "En cours", "Lu") à l'inscription.
    *   Possibilité pour le lecteur de créer, renommer et supprimer ses propres listes personnalisées.
    *   Interface simple pour ajouter ou retirer un livre de n'importe quelle liste (par défaut ou personnalisée).
    *   Fonctionnalité "Favoris" : Marquer / Démarquer un livre comme favori.
    *   Dashboard Lecteur (Page Profil) : Vue centralisée des listes de lecture et des favoris de l'utilisateur.
*   **Avis et Notes (Lecteur) :**
    *   Possibilité de soumettre **un seul avis** par livre, composé d'une note obligatoire (1-5 étoiles) et d'un commentaire textuel (optionnel).
    *   Possibilité de modifier sa note et/ou son commentaire ultérieurement.
    *   Possibilité de supprimer son propre avis.
    *   Affichage de l'indication "(modifié)" à côté des avis mis à jour.
*   **Administration (Admin) :**
    *   Tableau de bord administrateur simple.
    *   Interface pour Ajouter / Modifier / Supprimer des livres (incluant tous les champs sauf `pdf_url` géré par l'UI pour V1).
    *   Interface pour Ajouter / Modifier / Supprimer des genres.
    *   Interface de **Modération des Avis** : Afficher une liste d'avis (potentiellement les plus récents) avec la possibilité de les supprimer.

### 4. Exigences Fonctionnelles

*(EF = Exigence Fonctionnelle)*

*   **EF-Général-01 :** Le système doit afficher une page d'accueil/destination aux visiteurs et utilisateurs connectés.
*   **EF-Général-01a :** La page d'accueil doit contenir une section "hero" avec une barre de recherche fonctionnelle (titre/auteur).
*   **EF-Général-01b :** La page d'accueil doit présenter le concept et les fonctionnalités clés de Nookli.
*   **EF-Général-01c :** La page d'accueil doit afficher au moins une section de livres mis en avant (ex: les plus récents).
*   **EF-Général-02 :** Le système doit permettre à tout utilisateur de rechercher des livres par titre ou auteur via la barre de recherche.
*   **EF-Général-03 :** Le système doit permettre à tout utilisateur de voir une page listant les livres filtrés par un genre sélectionné.
*   **EF-Général-04 :** Le système doit permettre à tout utilisateur de visualiser la page de détails d'un livre, incluant ses informations, sa note moyenne et la liste des avis existants.
*   **EF-Général-05 :** Le système doit afficher une indication visuelle (ex: date différente, mention) si un avis a été mis à jour (`updated_at > created_at`).
*   **EF-Général-06 :** Le système doit permettre aux visiteurs de s'inscrire en fournissant un nom d'utilisateur unique, une adresse email unique et un mot de passe. Le mot de passe doit être haché avant stockage.
*   **EF-Général-07 :** Le système doit permettre aux utilisateurs de se connecter en utilisant leur email (ou nom d'utilisateur) et mot de passe.
*   **EF-Général-08 :** Le système doit permettre aux utilisateurs connectés de se déconnecter, mettant fin à leur session.

*   **EF-Lecteur-01 :** Le système doit créer automatiquement les listes "À lire", "En cours", "Lu" (avec `is_default=TRUE`) pour chaque nouvel utilisateur enregistré.
*   **EF-Lecteur-02 :** Le système doit permettre à un lecteur d'ajouter ou de retirer n'importe quel livre de n'importe laquelle de ses listes de lecture via l'interface utilisateur (ex: boutons sur la page détail ou carte de livre).
*   **EF-Lecteur-03 :** Le système doit permettre à un lecteur de créer une nouvelle liste de lecture personnalisée via son dashboard.
*   **EF-Lecteur-04 :** Le système doit permettre à un lecteur de renommer ou supprimer ses listes de lecture personnalisées via son dashboard.
*   **EF-Lecteur-05 :** Le système doit permettre à un lecteur d'ajouter ou de retirer un livre de sa liste de favoris via l'interface utilisateur.
*   **EF-Lecteur-06 :** Le système doit permettre à un lecteur de soumettre un et un seul avis (note obligatoire 1-5 + commentaire optionnel) pour un livre donné. La soumission doit échouer ou mettre à jour l'existant si un avis de cet utilisateur existe déjà pour ce livre (géré par la contrainte `UNIQUE(user_id, book_id)` et la logique du contrôleur).
*   **EF-Lecteur-07 :** Le système doit permettre à un lecteur de modifier la note et/ou le texte de son propre avis existant.
*   **EF-Lecteur-08 :** Le système doit permettre à un lecteur de supprimer son propre avis existant.
*   **EF-Lecteur-09 :** Le système doit fournir au lecteur une page "Dashboard" (Profil) affichant ses listes de lecture (avec les livres contenus) et ses favoris.

*   **EF-Admin-01 :** Le système doit fournir une section/interface d'administration distincte, accessible uniquement aux utilisateurs avec le rôle 'admin'.
*   **EF-Admin-02 :** Le système doit permettre à un admin d'ajouter un nouveau livre via un formulaire incluant titre, auteur, ISBN, description, URL couverture, année, sélection de genre.
*   **EF-Admin-03 :** Le système doit permettre à un admin de modifier les informations d'un livre existant.
*   **EF-Admin-04 :** Le système doit permettre à un admin de supprimer un livre existant.
*   **EF-Admin-05 :** Le système doit permettre à un admin de créer, modifier et supprimer des genres via l'interface d'administration.
*   **EF-Admin-06 :** Le système doit calculer et afficher la note moyenne d'un livre sur sa page de détails, basée sur la moyenne de toutes les notes (`rating`) des avis valides associés à ce livre. Cette moyenne doit être mise à jour après chaque ajout/modification/suppression d'avis.
*   **EF-Admin-07 :** Le système doit fournir à l'administrateur une interface pour visualiser une liste d'avis (ex: les plus récents) et la possibilité de les supprimer individuellement (modération).

### 5. Exigences Non Fonctionnelles

*(ENF = Exigence Non Fonctionnelle)*

*   **ENF-UIUX-01 :** L'interface utilisateur doit être intuitive, claire, esthétiquement agréable (thème "Nookli") et engageante, en particulier la page d'accueil.
*   **ENF-UIUX-02 :** L'application doit être développée avec une approche **Mobile First** et être entièrement **responsive**, garantissant une expérience utilisateur optimale sur mobiles, tablettes et ordinateurs de bureau.
*   **ENF-Perf-01 :** Les temps de chargement des pages et de réponse des interactions API doivent être raisonnables pour une bonne expérience utilisateur.
*   **ENF-Perf-02 :** Les requêtes à la base de données SQLite doivent être optimisées (utilisation d'index, sélection judicieuse des données).
*   **ENF-Secu-01 :** Les mots de passe utilisateurs doivent être stockés de manière sécurisée en utilisant l'algorithme de hachage `bcrypt`.
*   **ENF-Secu-02 :** L'accès aux différentes fonctionnalités (gestion des listes, soumission d'avis, administration) doit être strictement contrôlé en fonction de l'état de connexion et du rôle de l'utilisateur (`isAuthenticated`, `isAdmin` middlewares).
*   **ENF-Secu-03 :** Des mesures de base contre les vulnérabilités web courantes (ex: XSS via échappement des données dans EJS, potentiellement CSRF si pertinent) doivent être considérées.
*   **ENF-Code-01 :** Le code source (Backend et Frontend) doit adhérer aux standards JavaScript ES6+ et être bien structuré, lisible, et raisonnablement commenté pour faciliter la maintenance et la compréhension.
*   **ENF-Code-02 :** Le projet doit respecter la structure de dossiers Backend / Frontend définie, gérée par un `package.json` racine.
*   **ENF-Data-01 :** L'intégrité des données doit être assurée par l'utilisation correcte des clés primaires, clés étrangères (`FOREIGN KEY`) et contraintes d'unicité (`UNIQUE`) définies dans le schéma de la base de données. Les opérations `ON DELETE` (CASCADE, SET NULL) doivent être choisies judicieusement.
*   **ENF-Compat-01 :** L'application doit être compatible avec les versions récentes des navigateurs web courants (Chrome, Firefox, Safari, Edge).

### 6. User Flows / Stories Principales

*   **Découverte et Inscription :**
    *   *En tant que visiteur,* j'arrive sur la page d'accueil, je vois une invitation à chercher des livres et des exemples de ce que propose Nookli. Je décide de m'inscrire via le formulaire d'inscription.
*   **Recherche et Ajout à la Liste "À lire" :**
    *   *En tant que lecteur,* je me connecte. J'utilise la barre de recherche pour trouver "Dune". Je clique sur le résultat pour voir les détails. Intéressé, je clique sur le bouton "Ajouter à ma liste" et je sélectionne "À lire".
*   **Gestion du Suivi et Avis :**
    *   *En tant que lecteur,* je vais sur mon dashboard. Je vois "Dune" dans "À lire". Je commence à le lire, donc je le déplace vers "En cours". Une fois terminé, je le déplace vers "Lu". Je retourne sur la page de "Dune" et clique sur "Donner mon avis", je sélectionne 5 étoiles et écris un commentaire.
*   **Modification d'un Avis :**
    *   *En tant que lecteur,* je relis mon avis sur "Dune" et je veux nuancer mon propos. Je retourne sur la page du livre, je trouve mon avis et clique sur "Modifier". Je change le texte et valide. L'avis s'affiche avec la mention "(modifié)".
*   **Création d'une Liste Personnalisée :**
    *   *En tant que lecteur,* je veux regrouper les recommandations de mes amis. Je vais sur mon dashboard, clique sur "Créer une liste", l'appelle "Recommandations Amis", puis j'ajoute des livres à cette nouvelle liste.
*   **Administration : Ajout d'un Livre :**
    *   *En tant qu'administrateur,* je me connecte et accède au panneau d'administration. Je clique sur "Ajouter un livre", remplis le formulaire avec les informations d'un nouveau titre et valide. Le livre apparaît maintenant dans le catalogue public.
*   **Administration : Modération d'un Avis :**
    *   *En tant qu'administrateur,* je vais dans la section "Modération des Avis". Je vois un avis inapproprié et je clique sur le bouton "Supprimer" à côté de cet avis. Il disparaît de la page de détails du livre concerné.

### 7. Technologie Utilisée

*   **Backend :**
    *   Langage : JavaScript (ES6+)
    *   Environnement : Node.js
    *   Framework : Express.js
    *   Moteur de Template : EJS (Embedded JavaScript templates)
    *   Base de Données : SQLite 3
    *   ORM/Query Builder : Aucun (requêtes SQL directes via le driver `sqlite3`)
    *   Gestionnaire de Session : `express-session` + `connect-sqlite3`
    *   Authentification : `passport` + `passport-local`
    *   Hachage de Mot de Passe : `bcrypt`
    *   Variables d'Environnement : `dotenv`
    *   Développement : `nodemon`
*   **Frontend :**
    *   Structure : HTML5 (sémantique)
    *   Style : CSS3 / CSS4 (modules pertinents), approche Mobile First, Responsive Design (Media Queries).
    *   Logique Client : JavaScript (ES6+) natif (manipulation DOM, `fetch` pour API).
    *   Routage Client : Non utilisé pour la V1 (ou `page.js` très limité si besoin spécifique émerge).
*   **Outils & Divers :**
    *   Gestion de Paquets : `npm`
    *   Contrôle de Version : `Git` / `GitHub` (ou équivalent)

### 8. Structure du Projet

La structure suit l'organisation confirmée :

```
📁 NOOKLI-APP/             # Racine du projet
│
├── 📁 backend/             # Tout le code côté serveur
│   ├── 📁 config/          # Configuration (DB, Passport)
│   │   └── database.js
│   ├── 📁 controllers/      # Logique de requête/réponse
│   ├── 📁 database/         # Schéma SQL, potentiellement migrations/seeds
│   │   └── schema.sql
│   ├── 📁 middleware/       # Middlewares Express (auth, admin, errors)
│   ├── 📁 models/           # Classes/fonctions interagissant avec la DB
│   │   └── User.js         # Exemple
│   ├── 📁 routes/           # Définition des routes (pages et API)
│   │   ├── 📁 api/          # Routes API RESTful
│   │   └── index.js        # Routes principales (pages EJS)
│   ├── 📁 views/            # Fichiers template EJS
│   │   ├── 📁 partials/     # Fragments EJS réutilisables
│   │   ├── home.ejs
│   │   └── layout.ejs
│   └── server.js             # Point d'entrée du serveur Express
│
├── 📁 frontend/            # Assets statiques servis au client
│   ├── 📁 css/              # Fichiers CSS
│   │   └── style.css
│   ├── 📁 fonts/             # Polices personnalisées (si utilisées)
│   ├── 📁 img/              # Images (logos, couvertures par défaut?)
│   └── ┌── js/               # JavaScript côté client
│       ├── 📁 components/   # JS pour des UI spécifiques (ex: avis, listes)
│       ├── 📁 lib/          # Bibliothèques externes (ex: page.js si utilisé)
│       ├── api.js          # Module pour les appels fetch vers le backend
│       └── main.js         # Point d'entrée JS client, initialisations globales
│
├── node_modules/          # Dépendances (gérées par package.json racine)
├── .env                   # Variables d'environnement (ignoré par Git)
├── .env.example           # Exemple de .env
├── .gitignore             # Fichiers/dossiers ignorés par Git
├── nookli_dev.db          # Fichier base de données SQLite (ou dans backend/database/)
├── package-lock.json      # Verrouillage des versions des dépendances
└── package.json           # Dépendances et scripts du projet (racine)
```

### 9. Chronogramme Général de Développement

Ce chronogramme découpe le projet en phases séquentielles logiques.

1.  **Phase 0 : Initialisation & Setup (1-2 jours)**
    *   Créer la structure de dossiers.
    *   `npm init`, installer Express, EJS, Nodemon, SQLite3, dotenv.
    *   Configurer Git & `.gitignore`.
    *   Serveur Express de base, configuration EJS, `dotenv`.

2.  **Phase 1 : Base de Données & Modèles Fondamentaux (2-3 jours)**
    *   Finaliser `schema.sql` (Section 10), créer la DB.
    *   Implémenter `backend/config/database.js`.
    *   Créer les modèles `User.js`, `Book.js`, `Genre.js` avec méthodes CRUD basiques.

3.  **Phase 2 : Authentification Utilisateur (3-4 jours)**
    *   Configurer `express-session` & `connect-sqlite3`.
    *   Configurer `Passport` (stratégie locale, sérialisation/désérialisation).
    *   Intégrer `bcrypt` pour le hachage.
    *   Routes/Contrôleurs/Vues pour Inscription, Connexion, Déconnexion.
    *   Middleware `isAuthenticated`. Logique d'affichage conditionnel dans les vues.

4.  **Phase 3 : Catalogue Livres (Admin & Public) (4-5 jours)**
    *   Middleware `isAdmin`.
    *   CRUD Admin pour Livres et Genres (Routes/Contrôleurs/Vues EJS).
    *   Affichage public : Page d'accueil, page détails livre, recherche/filtrage (Routes/Contrôleurs/Vues EJS). Calcul initial de `average_rating`.

5.  **Phase 4 : Fonctionnalités Lecteur (Avis, Listes, Favoris) (5-7 jours)**
    *   Implémenter les modèles `Review.js`, `ReadingList.js`, `BookListEntry.js`, `Favorite.js`.
    *   Créer les Routes API et Contrôleurs pour Avis, Listes, Favoris.
    *   Développer le JS Frontend (`api.js`, `components/`) pour interagir avec les API (Ajouter/Modifier/Supprimer avis, Gérer listes, Gérer favoris).
    *   Mettre à jour l'UI dynamiquement.
    *   Créer/Finaliser la page Dashboard Lecteur (`profile.ejs`).
    *   Assurer la mise à jour de `average_rating` lors des actions sur les avis.

6.  **Phase 5 : Style & Expérience Utilisateur (Mobile First) (5-7 jours)**
    *   Développer `style.css` en commençant par les styles mobiles.
    *   Ajouter les media queries pour tablettes/bureaux.
    *   Styliser tous les composants : landing page, cartes de livre, formulaires, dashboard, pages admin.
    *   Assurer la cohérence et la navigabilité.

7.  **Phase 6 : Finalisation & Polissage (3-4 jours)**
    *   Gestion des erreurs (middleware).
    *   Validation des formulaires (client/serveur).
    *   Vérifier affichage "(modifié)" des avis.
    *   Implémenter l'interface de Modération Admin (liste + bouton supprimer).
    *   Tests manuels complets sur différents appareils/navigateurs.
    *   Nettoyage du code, ajout de commentaires.
    *   Mise à jour `README.md`.

*(Durées indicatives, peuvent varier selon l'expérience et le temps disponible)*

### 10. Schéma de la Base de Données (SQL Finalisé)

```sql
-- Suppression des tables existantes pour repartir de zéro (prudence en dev!)
DROP TABLE IF EXISTS FAVORITES;
DROP TABLE IF EXISTS BOOK_LIST_ENTRIES;
DROP TABLE IF EXISTS READING_LISTS;
DROP TABLE IF EXISTS REVIEWS;
DROP TABLE IF EXISTS BOOKS;
DROP TABLE IF EXISTS GENRES;
DROP TABLE IF EXISTS USERS;

-- =============================================
-- Table: USERS
-- Description: Stocke les informations des utilisateurs enregistrés.
-- =============================================
CREATE TABLE USERS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'utilisateur
    username TEXT UNIQUE NOT NULL,              -- Nom d'utilisateur choisi (unique)
    email TEXT UNIQUE NOT NULL,                 -- Adresse email (unique, pour connexion/contact)
    password_hash TEXT NOT NULL,                -- Mot de passe haché (bcrypt)
    role TEXT NOT NULL DEFAULT 'reader'         -- Rôle ('reader' ou 'admin')
        CHECK(role IN ('reader', 'admin')),
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création du compte
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Date de dernière mise à jour du compte
);

-- =============================================
-- Table: GENRES
-- Description: Stocke les genres littéraires possibles.
-- =============================================
CREATE TABLE GENRES (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique du genre
    name TEXT UNIQUE NOT NULL,                  -- Nom du genre (ex: 'Science-Fiction')
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création du genre
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Date de dernière mise à jour du genre
);

-- =============================================
-- Table: BOOKS
-- Description: Stocke les informations sur chaque livre du catalogue.
-- =============================================
CREATE TABLE BOOKS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique du livre
    title TEXT NOT NULL,                        -- Titre du livre
    author TEXT NOT NULL,                       -- Auteur(s) du livre
    isbn TEXT UNIQUE,                           -- ISBN (optionnel mais unique si fourni)
    description TEXT,                           -- Résumé ou description du livre
    cover_image_url TEXT,                       -- URL vers l'image de couverture
    publication_year INTEGER,                   -- Année de publication
    pdf_url TEXT NULL,                          -- Chemin/URL vers le fichier PDF (pour V2)
    genre_id INTEGER,                           -- Clé étrangère vers GENRES.id
    added_by_user_id INTEGER,                   -- Clé étrangère vers USERS.id (l'admin qui a ajouté)
    average_rating REAL DEFAULT 0.0,            -- Note moyenne calculée à partir des REVIEWS
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date d'ajout du livre à la base
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de dernière mise à jour du livre
    FOREIGN KEY (genre_id) REFERENCES GENRES(id)
        ON DELETE SET NULL,                     -- Si le genre est supprimé, le champ devient NULL
    FOREIGN KEY (added_by_user_id) REFERENCES USERS(id)
        ON DELETE SET NULL                      -- Si l'admin est supprimé, le champ devient NULL
);

-- =============================================
-- Table: REVIEWS
-- Description: Stocke les avis (note + commentaire) des utilisateurs sur les livres.
-- Contrainte: Un utilisateur ne peut laisser qu'un seul avis par livre.
-- =============================================
CREATE TABLE REVIEWS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'avis
    book_id INTEGER NOT NULL,                   -- Clé étrangère vers BOOKS.id
    user_id INTEGER NOT NULL,                   -- Clé étrangère vers USERS.id
    rating INTEGER NOT NULL                     -- Note donnée (1 à 5)
        CHECK(rating >= 1 AND rating <= 5),
    review_text TEXT,                           -- Commentaire textuel (peut être vide/NULL)
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création de l'avis
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de dernière modification de l'avis
    FOREIGN KEY (book_id) REFERENCES BOOKS(id)
        ON DELETE CASCADE,                      -- Si le livre est supprimé, ses avis le sont aussi
    FOREIGN KEY (user_id) REFERENCES USERS(id)
        ON DELETE CASCADE,                      -- Si l'utilisateur est supprimé, ses avis le sont aussi
    UNIQUE(user_id, book_id)                    -- Contrainte: un seul avis par user/livre
);

-- =============================================
-- Table: READING_LISTS
-- Description: Stocke les listes de lecture créées par les utilisateurs.
-- Inclut les listes par défaut ('À lire', 'En cours', 'Lu').
-- =============================================
CREATE TABLE READING_LISTS (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de la liste
    user_id INTEGER NOT NULL,                   -- Clé étrangère vers USERS.id (propriétaire)
    list_name TEXT NOT NULL,                    -- Nom de la liste (unique par utilisateur)
    is_default BOOLEAN NOT NULL DEFAULT FALSE,  -- TRUE pour les 3 listes créées à l'inscription
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de création de la liste
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date de dernière modification (ex: renommage)
    FOREIGN KEY (user_id) REFERENCES USERS(id)
        ON DELETE CASCADE,                      -- Si l'utilisateur est supprimé, ses listes aussi
    UNIQUE(user_id, list_name)                  -- Contrainte: nom de liste unique par utilisateur
);

-- =============================================
-- Table: BOOK_LIST_ENTRIES
-- Description: Table de jointure pour associer les livres aux listes de lecture.
-- =============================================
CREATE TABLE BOOK_LIST_ENTRIES (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'entrée
    list_id INTEGER NOT NULL,                   -- Clé étrangère vers READING_LISTS.id
    book_id INTEGER NOT NULL,                   -- Clé étrangère vers BOOKS.id
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date d'ajout du livre à la liste
    FOREIGN KEY (list_id) REFERENCES READING_LISTS(id)
        ON DELETE CASCADE,                      -- Si la liste est supprimée, les entrées aussi
    FOREIGN KEY (book_id) REFERENCES BOOKS(id)
        ON DELETE CASCADE,                      -- Si le livre est supprimé, il disparaît des listes
    UNIQUE(list_id, book_id)                    -- Contrainte: un livre ne peut être qu'une fois dans une liste
);

-- =============================================
-- Table: FAVORITES
-- Description: Table de jointure simple pour marquer les livres favoris d'un utilisateur.
-- =============================================
CREATE TABLE FAVORITES (
    id INTEGER PRIMARY KEY AUTOINCREMENT,       -- Identifiant unique de l'entrée favori
    user_id INTEGER NOT NULL,                   -- Clé étrangère vers USERS.id
    book_id INTEGER NOT NULL,                   -- Clé étrangère vers BOOKS.id
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Date d'ajout aux favoris
    FOREIGN KEY (user_id) REFERENCES USERS(id)
        ON DELETE CASCADE,                      -- Si l'utilisateur est supprimé, ses favoris aussi
    FOREIGN KEY (book_id) REFERENCES BOOKS(id)
        ON DELETE CASCADE,                      -- Si le livre est supprimé, il disparaît des favoris
    UNIQUE(user_id, book_id)                    -- Contrainte: un utilisateur ne favorise un livre qu'une fois
);

-- =============================================
-- Index pour améliorer les performances des requêtes courantes
-- (SQLite crée implicitement des index sur PK et UNIQUE, mais ceux-ci sont explicites)
-- =============================================
CREATE INDEX idx_books_title ON BOOKS(title);
CREATE INDEX idx_books_author ON BOOKS(author);
CREATE INDEX idx_books_genre_id ON BOOKS(genre_id);
CREATE INDEX idx_reviews_book_id ON REVIEWS(book_id);
CREATE INDEX idx_reviews_user_id ON REVIEWS(user_id);
CREATE INDEX idx_reading_lists_user_id ON READING_LISTS(user_id);
CREATE INDEX idx_book_list_entries_list_id ON BOOK_LIST_ENTRIES(list_id);
CREATE INDEX idx_book_list_entries_book_id ON BOOK_LIST_ENTRIES(book_id);
CREATE INDEX idx_favorites_user_id ON FAVORITES(user_id);
CREATE INDEX idx_favorites_book_id ON FAVORITES(book_id);

```

---
