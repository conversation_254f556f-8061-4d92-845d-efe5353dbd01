#!/usr/bin/env node

/**
 * Script avancé pour alimenter la base de données avec des livres depuis diverses API
 * Usage: node scripts/seed-book-db.js <nombre_de_livres> [options]
 *
 * Options:
 *   --admin-id <id>           ID de l'administrateur (défaut: 1)
 *   --languages <langs>       Langues séparées par des virgules (défaut: fr,en,it)
 *   --distribution <ratios>   Distribution des langues en % (défaut: 60,25,15)
 *   --exact                   Garantit le nombre exact de livres demandé
 *   --genres <genres>         Genres cibles séparés par des virgules
 *   --help                    Affiche cette aide
 *
 * Exemples:
 *   node scripts/seed-book-db.js 150
 *   node scripts/seed-book-db.js 100 --admin-id 2 --languages fr,en --distribution 70,30
 *   node scripts/seed-book-db.js 200 --exact --genres Fantasy,Science-Fiction,Romance
 *
 * Ce script utilise une distribution intelligente pour assurer la variété des livres
 * et télécharge automatiquement les images de couverture localement.
 */

import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { sequelize, Book, Genre } from '../backend/models/index.js';
import fileManager from '../backend/utils/fileManager.js';
import readline from 'readline';

// --- Calculer __dirname ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// --- Charger .env ---
const envPath = path.resolve(__dirname, '..', '.env');
dotenv.config({ path: envPath });

// Interface pour lire les entrées utilisateur
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Configuration de distribution par défaut
const DEFAULT_CONFIG = {
    languages: ['fr', 'en', 'it'],
    languageDistribution: [60, 25, 15], // Pourcentages
    statusDistribution: { published: 80, draft: 20 }, // Pourcentages
    yearDistribution: {
        recent: { range: [2020, 2024], percentage: 20 },
        modern: { range: [2000, 2019], percentage: 30 },
        contemporary: { range: [1980, 1999], percentage: 30 },
        classic: { range: [1900, 1979], percentage: 20 }
    },
    maxBooksPerAuthor: 3
};

// Liste étendue de termes de recherche pour obtenir des livres divers
const searchTermsByLanguage = {
    fr: [
        // Fiction
        'roman', 'fantasy', 'science fiction', 'romance', 'thriller', 'mystère', 'policier',
        'aventure', 'horreur', 'drame', 'comédie', 'humour', 'classique', 'contemporain',
        // Non-fiction
        'biographie', 'autobiographie', 'histoire', 'philosophie', 'psychologie', 'sociologie',
        'politique', 'économie', 'business', 'développement personnel', 'santé', 'cuisine',
        'voyage', 'art', 'musique', 'cinéma', 'littérature', 'poésie', 'théâtre',
        // Jeunesse
        'jeunesse', 'enfants', 'adolescent', 'conte', 'fable',
        // Spécialisés
        'essai', 'témoignage', 'guide', 'manuel', 'encyclopédie'
    ],
    en: [
        // Fiction
        'novel', 'fantasy', 'science fiction', 'romance', 'thriller', 'mystery', 'detective',
        'adventure', 'horror', 'drama', 'comedy', 'humor', 'classic', 'contemporary',
        'literary fiction', 'historical fiction', 'crime', 'suspense',
        // Non-fiction
        'biography', 'autobiography', 'memoir', 'history', 'philosophy', 'psychology',
        'sociology', 'politics', 'economics', 'business', 'self-help', 'health', 'cooking',
        'travel', 'art', 'music', 'cinema', 'literature', 'poetry', 'theater',
        // Young Adult & Children
        'young adult', 'children', 'teen', 'fairy tale', 'fable',
        // Specialized
        'essay', 'guide', 'manual', 'encyclopedia', 'textbook'
    ],
    it: [
        // Fiction
        'romanzo', 'fantasia', 'fantascienza', 'amore', 'thriller', 'mistero', 'giallo',
        'avventura', 'orrore', 'dramma', 'commedia', 'umorismo', 'classico', 'contemporaneo',
        // Non-fiction
        'biografia', 'autobiografia', 'storia', 'filosofia', 'psicologia', 'sociologia',
        'politica', 'economia', 'affari', 'auto-aiuto', 'salute', 'cucina', 'viaggio',
        'arte', 'musica', 'cinema', 'letteratura', 'poesia', 'teatro',
        // Giovani
        'giovani adulti', 'bambini', 'adolescenti', 'favola', 'fiaba',
        // Specializzati
        'saggio', 'guida', 'manuale', 'enciclopedia'
    ],
    es: [
        // Fiction
        'novela', 'fantasía', 'ciencia ficción', 'romance', 'thriller', 'misterio',
        'aventura', 'horror', 'drama', 'comedia', 'humor', 'clásico', 'contemporáneo',
        // Non-fiction
        'biografía', 'autobiografía', 'historia', 'filosofía', 'psicología', 'sociología',
        'política', 'economía', 'negocios', 'autoayuda', 'salud', 'cocina', 'viaje',
        'arte', 'música', 'cine', 'literatura', 'poesía', 'teatro',
        // Jóvenes
        'jóvenes adultos', 'niños', 'adolescentes', 'cuento', 'fábula',
        // Especializados
        'ensayo', 'guía', 'manual', 'enciclopedia'
    ],
    de: [
        // Fiction
        'roman', 'fantasy', 'science fiction', 'romantik', 'thriller', 'krimi',
        'abenteuer', 'horror', 'drama', 'komödie', 'humor', 'klassiker', 'zeitgenössisch',
        // Non-fiction
        'biographie', 'autobiographie', 'geschichte', 'philosophie', 'psychologie',
        'soziologie', 'politik', 'wirtschaft', 'business', 'selbsthilfe', 'gesundheit',
        'kochen', 'reisen', 'kunst', 'musik', 'kino', 'literatur', 'poesie', 'theater',
        // Jugend
        'jugendbuch', 'kinderbuch', 'märchen', 'fabel',
        // Spezialisiert
        'essay', 'ratgeber', 'handbuch', 'lexikon'
    ]
};

// Mapping des sujets OpenLibrary vers des genres
const subjectToGenreMap = {
    // Français
    'roman': 'Roman',
    'fantastique': 'Fantasy',
    'science-fiction': 'Science-Fiction',
    'science fiction': 'Science-Fiction',
    'sf': 'Science-Fiction',
    'fantasy': 'Fantasy',
    'romance': 'Romance',
    'thriller': 'Thriller',
    'mystère': 'Mystère',
    'policier': 'Mystère',
    'biographie': 'Biographie',
    'histoire': 'Histoire',
    'historique': 'Histoire',
    'philosophie': 'Philosophie',
    'poésie': 'Poésie',
    'classique': 'Classique',
    'jeunesse': 'Jeunesse',
    'enfants': 'Enfants',
    'aventure': 'Aventure',
    'horreur': 'Horreur',
    'drame': 'Drame',
    'comédie': 'Comédie',
    'humour': 'Comédie',
    'développement personnel': 'Développement personnel',
    'self-help': 'Développement personnel',
    'business': 'Business',
    'économie': 'Business',
    'cuisine': 'Cuisine',
    'gastronomie': 'Cuisine',
    'voyage': 'Voyage',
    'tourisme': 'Voyage',

    // English
    'novel': 'Roman',
    'fiction': 'Roman',
    'fantasy': 'Fantasy',
    'science fiction': 'Science-Fiction',
    'romance': 'Romance',
    'thriller': 'Thriller',
    'mystery': 'Mystère',
    'detective': 'Mystère',
    'biography': 'Biographie',
    'autobiography': 'Biographie',
    'history': 'Histoire',
    'historical': 'Histoire',
    'philosophy': 'Philosophie',
    'poetry': 'Poésie',
    'classic': 'Classique',
    'young adult': 'Jeunesse',
    'children': 'Enfants',
    'adventure': 'Aventure',
    'horror': 'Horreur',
    'drama': 'Drame',
    'comedy': 'Comédie',
    'humor': 'Comédie',
    'self-help': 'Développement personnel',
    'business': 'Business',
    'economics': 'Business',
    'cooking': 'Cuisine',
    'food': 'Cuisine',
    'travel': 'Voyage',

    // Italian
    'romanzo': 'Roman',
    'fantasia': 'Fantasy',
    'fantascienza': 'Science-Fiction',
    'amore': 'Romance',
    'thriller': 'Thriller',
    'mistero': 'Mystère',
    'giallo': 'Mystère',
    'biografia': 'Biographie',
    'autobiografia': 'Biographie',
    'storia': 'Histoire',
    'storico': 'Histoire',
    'filosofia': 'Philosophie',
    'poesia': 'Poésie',
    'classico': 'Classique',
    'giovani adulti': 'Jeunesse',
    'bambini': 'Enfants',
    'avventura': 'Aventure',
    'orrore': 'Horreur',
    'dramma': 'Drame',
    'commedia': 'Comédie',
    'umorismo': 'Comédie',
    'auto-aiuto': 'Développement personnel',
    'affari': 'Business',
    'economia': 'Business',
    'cucina': 'Cuisine',
    'cibo': 'Cuisine',
    'viaggio': 'Voyage'
};

// Liste de genres à créer si nécessaire
const genresToCreate = [
    { name: 'Roman', description: 'Romans et fiction générale' },
    { name: 'Fantasy', description: 'Livres de fantasy et mondes imaginaires' },
    { name: 'Science-Fiction', description: 'Livres de science-fiction et futuristes' },
    { name: 'Romance', description: 'Histoires d\'amour et relations' },
    { name: 'Thriller', description: 'Livres à suspense et d\'action' },
    { name: 'Mystère', description: 'Enquêtes et mystères à résoudre' },
    { name: 'Biographie', description: 'Récits de vie et autobiographies' },
    { name: 'Histoire', description: 'Livres sur des événements historiques' },
    { name: 'Philosophie', description: 'Réflexions et pensées philosophiques' },
    { name: 'Poésie', description: 'Recueils de poèmes et œuvres poétiques' },
    { name: 'Classique', description: 'Œuvres littéraires classiques' },
    { name: 'Jeunesse', description: 'Livres pour adolescents et jeunes adultes' },
    { name: 'Enfants', description: 'Livres pour enfants' },
    { name: 'Aventure', description: 'Récits d\'aventures et d\'exploration' },
    { name: 'Horreur', description: 'Livres effrayants et d\'horreur' },
    { name: 'Drame', description: 'Histoires dramatiques et émotionnelles' },
    { name: 'Comédie', description: 'Livres humoristiques et comiques' },
    { name: 'Développement personnel', description: 'Livres d\'aide et de développement personnel' },
    { name: 'Business', description: 'Livres sur les affaires et l\'entrepreneuriat' },
    { name: 'Cuisine', description: 'Livres de recettes et de gastronomie' },
    { name: 'Voyage', description: 'Récits de voyage et guides touristiques' }
];

/**
 * Gestionnaire de distribution intelligente des livres
 */
class DistributionManager {
    constructor(totalBooks, config = DEFAULT_CONFIG) {
        this.totalBooks = totalBooks;
        this.config = config;
        this.progress = {
            total: 0,
            byLanguage: {},
            byGenre: {},
            byAuthor: {},
            byStatus: { published: 0, draft: 0 },
            byYear: { recent: 0, modern: 0, contemporary: 0, classic: 0 }
        };

        // Initialiser les compteurs
        this.config.languages.forEach(lang => {
            this.progress.byLanguage[lang] = 0;
        });

        this.calculateTargets();
    }

    calculateTargets() {
        this.targets = {
            byLanguage: {},
            byStatus: {},
            byYear: {}
        };

        // Calculer les cibles par langue
        this.config.languages.forEach((lang, index) => {
            const percentage = this.config.languageDistribution[index] || 0;
            this.targets.byLanguage[lang] = Math.round(this.totalBooks * percentage / 100);
        });

        // Calculer les cibles par statut
        this.targets.byStatus.published = Math.round(this.totalBooks * this.config.statusDistribution.published / 100);
        this.targets.byStatus.draft = this.totalBooks - this.targets.byStatus.published;

        // Calculer les cibles par période
        Object.entries(this.config.yearDistribution).forEach(([period, config]) => {
            this.targets.byYear[period] = Math.round(this.totalBooks * config.percentage / 100);
        });
    }

    getPreferredLanguage() {
        // Retourner la langue qui a le plus besoin de livres
        let preferredLang = this.config.languages[0];
        let maxNeed = 0;

        for (const lang of this.config.languages) {
            const target = this.targets.byLanguage[lang] || 0;
            const current = this.progress.byLanguage[lang] || 0;
            const need = target - current;

            if (need > maxNeed) {
                maxNeed = need;
                preferredLang = lang;
            }
        }

        return preferredLang;
    }

    getPreferredStatus() {
        const publishedNeed = this.targets.byStatus.published - this.progress.byStatus.published;
        const draftNeed = this.targets.byStatus.draft - this.progress.byStatus.draft;

        return publishedNeed > draftNeed ? 'published' : 'draft';
    }

    shouldAcceptAuthor(author) {
        const currentCount = this.progress.byAuthor[author] || 0;
        return currentCount < this.config.maxBooksPerAuthor;
    }

    recordBook(bookData) {
        this.progress.total++;
        this.progress.byLanguage[bookData.language] = (this.progress.byLanguage[bookData.language] || 0) + 1;
        this.progress.byAuthor[bookData.author] = (this.progress.byAuthor[bookData.author] || 0) + 1;
        this.progress.byStatus[bookData.status]++;

        // Enregistrer par période d'année
        if (bookData.publication_year) {
            const year = bookData.publication_year;
            if (year >= 2020) this.progress.byYear.recent++;
            else if (year >= 2000) this.progress.byYear.modern++;
            else if (year >= 1980) this.progress.byYear.contemporary++;
            else this.progress.byYear.classic++;
        }
    }

    getProgress() {
        return {
            completed: this.progress.total,
            remaining: this.totalBooks - this.progress.total,
            percentage: Math.round((this.progress.total / this.totalBooks) * 100),
            details: this.progress
        };
    }
}



/**
 * Fonction utilitaire pour attendre un certain temps
 * @param {number} ms - Temps d'attente en millisecondes
 * @returns {Promise<void>}
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Valide les données d'un livre reçues d'OpenLibrary
 * @param {Object} book - Données du livre à valider
 * @returns {boolean} - true si les données sont valides
 */
function validateOpenLibraryBook(book) {
    // Vérifications minimales requises
    if (!book || typeof book !== 'object') {
        return false;
    }

    // Un livre doit avoir au minimum un titre
    if (!book.title || typeof book.title !== 'string' || book.title.trim().length === 0) {
        return false;
    }

    // Vérifier que les champs essentiels sont du bon type s'ils existent
    if (book.author_name && !Array.isArray(book.author_name)) {
        return false;
    }

    if (book.isbn && !Array.isArray(book.isbn)) {
        return false;
    }

    if (book.first_publish_year && typeof book.first_publish_year !== 'number') {
        return false;
    }

    if (book.language && !Array.isArray(book.language)) {
        return false;
    }

    // Vérifier le rating s'il existe
    if (book.ratings_average && (typeof book.ratings_average !== 'number' || book.ratings_average < 0 || book.ratings_average > 5)) {
        return false;
    }

    return true;
}

/**
 * Génère un ISBN-13 valide (pour les livres sans ISBN)
 * @returns {string} - ISBN-13 généré
 */
function generateISBN13() {
    // Préfixe 978 pour les livres
    let isbn = '978';

    // Ajouter 9 chiffres aléatoires
    for (let i = 0; i < 9; i++) {
        isbn += Math.floor(Math.random() * 10);
    }

    // Calculer le chiffre de contrôle
    let sum = 0;
    for (let i = 0; i < 12; i++) {
        const digit = parseInt(isbn[i]);
        sum += (i % 2 === 0) ? digit : digit * 3;
    }

    const checkDigit = (10 - (sum % 10)) % 10;
    isbn += checkDigit;

    return isbn;
}

/**
 * Améliore la qualité des données d'un livre
 * @param {Object} bookData - Données du livre à améliorer
 * @returns {Object} - Données améliorées
 */
function enhanceBookData(bookData) {
    // Générer un ISBN si manquant
    if (!bookData.isbn) {
        bookData.isbn = generateISBN13();
    }

    // Améliorer le résumé si trop court
    if (!bookData.summary || bookData.summary.length < 50) {
        bookData.summary = `${bookData.title} par ${bookData.author}. Un livre captivant qui explore des thèmes profonds et offre une expérience de lecture enrichissante.`;
    }

    // Estimer le nombre de pages si manquant (basé sur le genre et la description)
    if (!bookData.page_count) {
        let estimatedPages = 250; // Valeur par défaut

        if (bookData.description) {
            // Plus la description est longue, plus le livre est probablement long
            estimatedPages = Math.min(600, Math.max(150, bookData.description.length * 0.5));
        }

        // Ajuster selon le genre (approximatif)
        const genreAdjustments = {
            'Enfants': 0.3,
            'Jeunesse': 0.7,
            'Poésie': 0.4,
            'Fantasy': 1.5,
            'Science-Fiction': 1.3,
            'Histoire': 1.2,
            'Biographie': 1.4
        };

        // Trouver le genre dans la map (approximatif)
        for (const [genreName, multiplier] of Object.entries(genreAdjustments)) {
            if (bookData.genre_name && bookData.genre_name.includes(genreName)) {
                estimatedPages *= multiplier;
                break;
            }
        }

        bookData.page_count = Math.round(estimatedPages);
    }

    return bookData;
}

/**
 * Récupère des livres depuis l'API OpenLibrary avec retries
 * @param {string} searchTerm - Terme de recherche
 * @param {string} language - Code de langue (fr, en, it)
 * @param {number} limit - Nombre maximum de résultats
 * @param {number} retries - Nombre de tentatives en cas d'échec
 * @returns {Promise<Array>} - Liste des livres
 */
async function fetchBooksFromOpenLibrary(searchTerm, language = 'fr', limit = 10, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            // Note: Le paramètre language d'OpenLibrary est trop restrictif, on préfère filtrer après
            // const langParam = language ? `&language=${language}` : '';

            // Optimisation : ne récupérer que les champs nécessaires (incluant ratings_average)
            const fields = 'key,title,author_name,isbn,cover_i,first_publish_year,language,first_sentence,ratings_average';
            const url = `https://openlibrary.org/search.json?q=${encodeURIComponent(searchTerm)}&limit=${limit}&fields=${fields}`;

            console.log(`Tentative ${attempt}/${retries} pour "${searchTerm}" (${language}): ${url}`);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 1000); // 1 secondes timeout

            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'User-Agent': 'Nookli-App/1.0 (educational project)'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            // Attendre un peu entre les requêtes pour ne pas surcharger l'API
            await sleep(1000);

            return data.docs || [];
        } catch (error) {
            console.error(`Tentative ${attempt}/${retries} échouée pour "${searchTerm}" (${language}):`, error.message);

            if (attempt < retries) {
                // Attendre de plus en plus longtemps entre les tentatives
                const waitTime = 2000 * attempt;
                console.log(`Nouvelle tentative dans ${waitTime/1000} secondes...`);
                await sleep(waitTime);
            } else {
                console.error(`Toutes les tentatives ont échoué pour "${searchTerm}" (${language}).`);
                return [];
            }
        }
    }

    return [];
}

/**
 * Récupère des détails supplémentaires sur un livre depuis OpenLibrary avec retries
 * @param {string} olid - OpenLibrary ID
 * @param {number} retries - Nombre de tentatives en cas d'échec
 * @returns {Promise<Object>} - Détails du livre
 */
async function fetchBookDetails(olid, retries = 3) {
    if (!olid) return null;

    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            const url = `https://openlibrary.org/works/${olid}.json`;

            console.log(`Récupération des détails pour ${olid}, tentative ${attempt}/${retries}`);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 1000); // 15 secondes timeout

            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'User-Agent': 'Nookli-App/1.0 (educational project)'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            // Attendre un peu entre les requêtes pour ne pas surcharger l'API
            await sleep(1000);

            return data;
        } catch (error) {
            console.error(`Tentative ${attempt}/${retries} échouée pour les détails de ${olid}:`, error.message);

            if (attempt < retries) {
                // Attendre de plus en plus longtemps entre les tentatives
                const waitTime = 2000 * attempt;
                console.log(`Nouvelle tentative dans ${waitTime/1000} secondes...`);
                await sleep(waitTime);
            } else {
                console.error(`Toutes les tentatives ont échoué pour les détails de ${olid}.`);
                return null;
            }
        }
    }

    return null;
}

/**
 * Nettoie une description HTML
 * @param {string} html - Texte HTML à nettoyer
 * @returns {string} - Texte nettoyé
 */
function cleanDescription(html) {
    if (!html) return null;

    // Supprimer les balises HTML
    let text = html.replace(/<[^>]*>/g, ' ');

    // Supprimer les espaces multiples
    text = text.replace(/\s+/g, ' ');

    // Limiter la longueur
    if (text.length > 2000) {
        text = text.substring(0, 1997) + '...';
    }

    return text.trim();
}

/**
 * Détecte la langue d'un livre à partir de ses métadonnées
 * @param {Object} bookData - Données du livre
 * @param {string} defaultLanguage - Langue par défaut
 * @returns {string} - Code de langue (fr, en, it, es, de)
 */
function detectBookLanguage(bookData, defaultLanguage = 'fr') {
    // Pour OpenLibrary - mapping complet des codes ISO 639-2/639-1
    if (bookData.language && Array.isArray(bookData.language)) {
        const langMap = {
            // Français
            'fre': 'fr', 'fra': 'fr', 'french': 'fr',
            // Anglais
            'eng': 'en', 'english': 'en',
            // Italien
            'ita': 'it', 'italian': 'it',
            // Espagnol
            'spa': 'es', 'spanish': 'es', 'espanol': 'es',
            // Allemand
            'ger': 'de', 'deu': 'de', 'german': 'de', 'deutsch': 'de'
        };

        for (const lang of bookData.language) {
            const langLower = lang.toLowerCase();

            // Vérifier le mapping
            if (langMap[langLower]) {
                return langMap[langLower];
            }

            // Vérifier les codes directs
            if (['fr', 'en', 'it', 'es', 'de'].includes(langLower)) {
                return langLower;
            }
        }
    }

    return defaultLanguage;
}

/**
 * Détermine le genre d'un livre à partir de ses sujets
 * @param {Array} subjects - Liste des sujets du livre
 * @param {Map} genreMap - Map des genres disponibles
 * @returns {number} - ID du genre
 */
function determineGenreFromSubjects(subjects, genreMap) {
    if (!subjects || !Array.isArray(subjects) || subjects.length === 0) {
        // Genre par défaut (Roman)
        return genreMap.get('Roman') || genreMap.values().next().value;
    }

    // Parcourir les sujets et trouver le premier qui correspond à un genre connu
    for (const subject of subjects) {
        const subjectLower = subject.toLowerCase();

        for (const [key, genreName] of Object.entries(subjectToGenreMap)) {
            if (subjectLower.includes(key)) {
                if (genreMap.has(genreName)) {
                    return genreMap.get(genreName);
                }
            }
        }
    }

    // Genre par défaut si aucun sujet ne correspond
    return genreMap.get('Roman') || genreMap.values().next().value;
}

/**
 * Transforme un livre d'OpenLibrary au format Nookli
 * @param {Object} openLibraryBook - Livre d'OpenLibrary
 * @param {Map} genreMap - Map des genres disponibles
 * @param {number} adminId - ID de l'administrateur
 * @param {string} defaultLanguage - Langue par défaut
 * @returns {Promise<Object>} - Livre au format Nookli
 */
async function transformOpenLibraryBook(openLibraryBook, genreMap, adminId, defaultLanguage = 'fr') {
    // Récupérer l'URL de la couverture
    let coverImageUrl = null;
    if (openLibraryBook.cover_i) {
        coverImageUrl = `https://covers.openlibrary.org/b/id/${openLibraryBook.cover_i}-L.jpg`;
    }

    // Détecter la langue
    const language = detectBookLanguage(openLibraryBook, defaultLanguage);

    // Récupérer des détails supplémentaires si possible
    let bookDetails = null;
    let description = null;
    let subjects = [];

    if (openLibraryBook.key) {
        const olid = openLibraryBook.key.split('/').pop();
        bookDetails = await fetchBookDetails(olid);

        if (bookDetails) {
            description = bookDetails.description ?
                (typeof bookDetails.description === 'string' ?
                    bookDetails.description :
                    bookDetails.description.value) :
                null;

            subjects = bookDetails.subjects || [];
        }
    }

    // Si pas de description détaillée, utiliser la première phrase
    if (!description && openLibraryBook.first_sentence) {
        description = openLibraryBook.first_sentence;
    }

    // Nettoyer la description
    description = cleanDescription(description);

    // Préparer les données de base pour générer le slug
    const baseBookData = {
        title: openLibraryBook.title || 'Titre inconnu',
        author: (openLibraryBook.author_name && openLibraryBook.author_name.join(', ')) || 'Auteur inconnu',
        language: language,
        publication_year: openLibraryBook.first_publish_year || null
    };

    // Générer le slug avant de télécharger la couverture
    const slug = Book.generateSlug(baseBookData);

    // Télécharger l'image de couverture si disponible
    let localCoverPath = null;

    if (coverImageUrl) {
        try {
            const coverInfo = await fileManager.downloadCoverImage(coverImageUrl, slug);
            localCoverPath = coverInfo.relativePath;
        } catch (error) {
            console.error(`Erreur lors du téléchargement de la couverture pour "${openLibraryBook.title}":`, error);
        }
    }

    // Extraire une version courte pour le résumé (premiers 200 caractères)
    const summary = description ?
        description.substring(0, 200) + (description.length > 200 ? '...' : '') :
        `Livre: ${openLibraryBook.title}`;

    // Déterminer le genre à partir des sujets
    const genreId = determineGenreFromSubjects(subjects, genreMap);

    // Calculer une note initiale si disponible depuis OpenLibrary
    let initialRating = null;
    if (openLibraryBook.ratings_average && typeof openLibraryBook.ratings_average === 'number') {
        initialRating = openLibraryBook.ratings_average

        console.log(`📊 Rating OpenLibrary: ${openLibraryBook.ratings_average}/5`);
    }

    // Déterminer le statut (20% des livres en draft)
    const status = Math.random() < 0.2 ? 'draft' : 'published';

    // Préparer les données du livre
    let bookData = {
        title: baseBookData.title,
        author: baseBookData.author,
        isbn: (openLibraryBook.isbn && openLibraryBook.isbn[0]) || null,
        summary: summary,
        description: description,
        cover_image_url: localCoverPath || coverImageUrl,
        publication_year: baseBookData.publication_year,
        pdf_url: null,
        genre_id: genreId,
        created_by: adminId,
        initial_rating: initialRating,
        language: baseBookData.language,
        status: status,
        slug: slug
    };

    // Améliorer la qualité des données
    bookData = enhanceBookData(bookData);

    return bookData;
}

/**
 * Crée les genres dans la base de données
 * @returns {Promise<Map>} - Map des genres (nom -> id)
 */
async function createGenres() {
    const genreMap = new Map();

    try {
        // Récupérer les genres existants
        const existingGenres = await Genre.findAll();
        existingGenres.forEach(genre => {
            genreMap.set(genre.name, genre.id);
        });

        console.log(`${existingGenres.length} genres existants trouvés.`);

        // Créer les genres manquants
        for (const genreData of genresToCreate) {
            if (!genreMap.has(genreData.name)) {
                try {
                    const newGenre = await Genre.create(genreData);
                    genreMap.set(newGenre.name, newGenre.id);
                    console.log(`Genre créé: ${newGenre.name} (ID: ${newGenre.id})`);
                } catch (error) {
                    console.error(`Erreur lors de la création du genre ${genreData.name}:`, error);
                }
            }
        }

        console.log(`Total de ${genreMap.size} genres disponibles.`);
        return genreMap;
    } catch (error) {
        console.error('Erreur lors de la récupération/création des genres:', error);
        throw error;
    }
}

/**
 * Importe des livres dans la base de données avec distribution intelligente
 * @param {number} count - Nombre de livres à importer
 * @param {number} adminId - ID de l'administrateur
 * @param {Object} options - Options d'importation
 */
async function importBooksWithDistribution(count, adminId, options = {}) {
    const { distributionManager, exact = false, targetGenres = null } = options;

    try {
        console.log(`Début de l'importation de ${count} livres avec distribution intelligente...`);

        // S'assurer que les dossiers de stockage existent
        fileManager.ensureDirectoriesExist();

        // Créer les genres
        const genreMap = await createGenres();

        // Filtrer les genres cibles si spécifiés
        let targetGenreIds = null;
        if (targetGenres) {
            targetGenreIds = [];
            for (const genreName of targetGenres) {
                if (genreMap.has(genreName)) {
                    targetGenreIds.push(genreMap.get(genreName));
                } else {
                    console.warn(`Genre non trouvé: ${genreName}`);
                }
            }
            if (targetGenreIds.length === 0) {
                throw new Error('Aucun genre cible valide trouvé');
            }
        }

        let booksImported = 0;
        let booksAttempted = 0;
        const maxAttempts = exact ? count * 10 : count * 3; // Plus de tentatives en mode exact

        // Parcourir jusqu'à avoir importé le nombre de livres demandé
        while (booksImported < count && booksAttempted < maxAttempts) {
            // Obtenir la langue préférée selon la distribution
            const preferredLanguage = distributionManager.getPreferredLanguage();
            const searchTerms = searchTermsByLanguage[preferredLanguage] || searchTermsByLanguage.fr;

            // Choisir un terme de recherche aléatoire
            const searchTerm = searchTerms[Math.floor(Math.random() * searchTerms.length)];

            // Récupérer les livres
            const books = await fetchBooksFromOpenLibrary(searchTerm, preferredLanguage, 5);

            console.log(`Recherche "${searchTerm}" en ${preferredLanguage}: ${books.length} livres trouvés`);

            // Afficher le progrès
            const progress = distributionManager.getProgress();
            console.log(`Progrès: ${progress.completed}/${count} (${progress.percentage}%)`);
            console.log(`Distribution actuelle: ${Object.entries(progress.details.byLanguage).map(([lang, count]) => `${lang}:${count}`).join(', ')}`);

            // Parcourir les livres récupérés
            for (const book of books) {
                if (booksImported >= count) break;

                try {
                    // Valider les données du livre avant traitement
                    if (!validateOpenLibraryBook(book)) {
                        console.log(`Livre ignoré - données invalides: ${book.title || 'titre manquant'}`);
                        continue;
                    }

                    // Transformer le livre avec la langue préférée
                    const bookData = await transformOpenLibraryBook(book, genreMap, adminId, preferredLanguage);

                    // Vérifier les critères de distribution
                    if (!distributionManager.shouldAcceptAuthor(bookData.author)) {
                        console.log(`Auteur déjà trop représenté: ${bookData.author}`);
                        continue;
                    }

                    // Filtrer par genres cibles si spécifiés
                    if (targetGenreIds && !targetGenreIds.includes(bookData.genre_id)) {
                        continue;
                    }

                    // Ajuster le statut selon la distribution préférée
                    bookData.status = distributionManager.getPreferredStatus();

                    // Vérifier si le livre existe déjà (par ISBN ou titre+auteur)
                    let existingBook = null;
                    if (bookData.isbn) {
                        // Vérifier par ISBN
                        existingBook = await Book.findOne({
                            where: { isbn: bookData.isbn }
                        });
                    }

                    if (!existingBook) {
                        // Vérifier par titre et auteur
                        existingBook = await Book.findOne({
                            where: {
                                title: bookData.title,
                                author: bookData.author
                            }
                        });
                    }

                    if (existingBook) {
                        console.log(`Livre déjà existant: ${bookData.title} par ${bookData.author}`);
                        continue;
                    }

                    // Créer le livre
                    const newBook = await Book.create(bookData);

                    // Enregistrer dans le gestionnaire de distribution
                    distributionManager.recordBook(bookData);
                    booksImported++;

                    console.log(`✓ Livre importé (${booksImported}/${count}): ${newBook.title} par ${newBook.author} [${newBook.language}] - ${newBook.status}`);
                } catch (error) {
                    console.error(`✗ Erreur lors de l'importation du livre:`, error.message);
                }

                booksAttempted++;
            }
        }

        // Rapport final détaillé
        const finalProgress = distributionManager.getProgress();
        console.log(`\n=== RAPPORT D'IMPORTATION ===`);
        console.log(`Livres importés: ${booksImported}/${count} (${finalProgress.percentage}%)`);
        console.log(`Tentatives totales: ${booksAttempted}`);

        console.log(`\nDistribution par langue:`);
        Object.entries(finalProgress.details.byLanguage).forEach(([lang, count]) => {
            const target = distributionManager.targets.byLanguage[lang] || 0;
            console.log(`  ${lang}: ${count}/${target} (${Math.round(count/booksImported*100)}%)`);
        });

        console.log(`\nDistribution par statut:`);
        Object.entries(finalProgress.details.byStatus).forEach(([status, count]) => {
            const target = distributionManager.targets.byStatus[status] || 0;
            console.log(`  ${status}: ${count}/${target} (${Math.round(count/booksImported*100)}%)`);
        });

        console.log(`\nAuteurs les plus représentés:`);
        const sortedAuthors = Object.entries(finalProgress.details.byAuthor)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);
        sortedAuthors.forEach(([author, count]) => {
            console.log(`  ${author}: ${count} livre(s)`);
        });

        if (exact && booksImported < count) {
            console.log(`\n⚠️  Mode exact activé mais seulement ${booksImported}/${count} livres importés.`);
            console.log(`Raisons possibles: livres déjà existants, API limitée, critères trop restrictifs.`);
        } else if (booksImported < count) {
            console.log(`\n⚠️  ${booksImported}/${count} livres importés. Certains livres étaient peut-être déjà présents.`);
        } else {
            console.log(`\n✅ Importation réussie! ${booksImported} livres ajoutés avec une distribution équilibrée.`);
        }
    } catch (error) {
        console.error('Erreur lors de l\'importation des livres:', error);
        throw error;
    }
}

/**
 * Affiche l'aide du script
 */
function showHelp() {
    console.log(`
Usage: node scripts/seed-book-db.js <nombre_de_livres> [options]

Arguments:
  nombre_de_livres    Nombre de livres à générer (obligatoire)

Options:
  --admin-id <id>           ID de l'administrateur (défaut: 1)
  --languages <langs>       Langues séparées par des virgules (défaut: fr,en,it)
  --distribution <ratios>   Distribution des langues en % (défaut: 60,25,15)
  --exact                   Garantit le nombre exact de livres demandé
  --genres <genres>         Genres cibles séparés par des virgules
  --help                    Affiche cette aide

Exemples:
  node scripts/seed-book-db.js 150
  node scripts/seed-book-db.js 100 --admin-id 2 --languages fr,en --distribution 70,30
  node scripts/seed-book-db.js 200 --exact --genres Fantasy,Science-Fiction,Romance

Langues supportées: fr, en, it, es, de
    `);
}

/**
 * Parse les arguments de la ligne de commande
 */
function parseArguments() {
    const args = process.argv.slice(2);

    if (args.length === 0 || args.includes('--help')) {
        showHelp();
        process.exit(0);
    }

    const config = {
        count: null,
        adminId: 1,
        languages: ['fr', 'en', 'it'],
        languageDistribution: [60, 25, 15],
        exact: false,
        targetGenres: null
    };

    // Premier argument doit être le nombre de livres
    config.count = parseInt(args[0]);
    if (isNaN(config.count) || config.count <= 0) {
        console.error('Erreur: Le nombre de livres doit être un entier positif.');
        process.exit(1);
    }

    // Parser les options
    for (let i = 1; i < args.length; i++) {
        const arg = args[i];

        switch (arg) {
            case '--admin-id':
                if (i + 1 >= args.length) {
                    console.error('Erreur: --admin-id nécessite une valeur.');
                    process.exit(1);
                }
                config.adminId = parseInt(args[++i]);
                if (isNaN(config.adminId) || config.adminId <= 0) {
                    console.error('Erreur: L\'ID de l\'administrateur doit être un entier positif.');
                    process.exit(1);
                }
                break;

            case '--languages':
                if (i + 1 >= args.length) {
                    console.error('Erreur: --languages nécessite une valeur.');
                    process.exit(1);
                }
                config.languages = args[++i].split(',').map(lang => lang.trim().toLowerCase());
                // Valider les langues
                const supportedLanguages = ['fr', 'en', 'it', 'es', 'de'];
                for (const lang of config.languages) {
                    if (!supportedLanguages.includes(lang)) {
                        console.error(`Erreur: Langue non supportée: ${lang}. Langues supportées: ${supportedLanguages.join(', ')}`);
                        process.exit(1);
                    }
                }
                break;

            case '--distribution':
                if (i + 1 >= args.length) {
                    console.error('Erreur: --distribution nécessite une valeur.');
                    process.exit(1);
                }
                config.languageDistribution = args[++i].split(',').map(ratio => {
                    const num = parseInt(ratio.trim());
                    if (isNaN(num) || num < 0 || num > 100) {
                        console.error('Erreur: Les ratios de distribution doivent être des entiers entre 0 et 100.');
                        process.exit(1);
                    }
                    return num;
                });
                break;

            case '--exact':
                config.exact = true;
                break;

            case '--genres':
                if (i + 1 >= args.length) {
                    console.error('Erreur: --genres nécessite une valeur.');
                    process.exit(1);
                }
                config.targetGenres = args[++i].split(',').map(genre => genre.trim());
                break;

            default:
                console.error(`Erreur: Option inconnue: ${arg}`);
                process.exit(1);
        }
    }

    // Ajuster la distribution par défaut si nécessaire
    if (config.languageDistribution.length !== config.languages.length) {
        // Si une seule langue est spécifiée, lui donner 100%
        if (config.languages.length === 1) {
            config.languageDistribution = [100];
        } else {
            // Sinon, distribuer équitablement
            const equalShare = Math.floor(100 / config.languages.length);
            config.languageDistribution = new Array(config.languages.length).fill(equalShare);
            // Ajuster le dernier pour atteindre 100%
            const remainder = 100 - (equalShare * config.languages.length);
            config.languageDistribution[config.languageDistribution.length - 1] += remainder;
        }
        console.log(`Distribution ajustée automatiquement: ${config.languageDistribution.join('%, ')}%`);
    }

    const totalDistribution = config.languageDistribution.reduce((sum, ratio) => sum + ratio, 0);
    if (totalDistribution !== 100) {
        console.error(`Erreur: La somme des ratios de distribution doit être 100 (actuellement: ${totalDistribution}).`);
        process.exit(1);
    }

    return config;
}

/**
 * Fonction principale
 */
async function main() {
    try {
        const config = parseArguments();

        console.log('Configuration:');
        console.log(`- Nombre de livres: ${config.count}`);
        console.log(`- ID administrateur: ${config.adminId}`);
        console.log(`- Langues: ${config.languages.join(', ')}`);
        console.log(`- Distribution: ${config.languageDistribution.join('%, ')}%`);
        console.log(`- Mode exact: ${config.exact ? 'Oui' : 'Non'}`);
        if (config.targetGenres) {
            console.log(`- Genres cibles: ${config.targetGenres.join(', ')}`);
        }
        console.log('');

        // Créer le gestionnaire de distribution
        const distributionConfig = {
            languages: config.languages,
            languageDistribution: config.languageDistribution,
            statusDistribution: DEFAULT_CONFIG.statusDistribution,
            yearDistribution: DEFAULT_CONFIG.yearDistribution,
            maxBooksPerAuthor: DEFAULT_CONFIG.maxBooksPerAuthor
        };

        const distributionManager = new DistributionManager(config.count, distributionConfig);

        // Importer les livres avec distribution intelligente
        await importBooksWithDistribution(config.count, config.adminId, {
            distributionManager,
            exact: config.exact,
            targetGenres: config.targetGenres
        });

    } catch (error) {
        console.error(`Erreur: ${error.message}`);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    } finally {
        rl.close();
        // Fermer la connexion Sequelize proprement
        try {
            await sequelize.close();
        } catch (error) {
            console.error('Erreur lors de la fermeture de la connexion:', error);
        }
    }
}

// Exécuter le script
main();
